// import { useRef } from 'react';
// import { useDebounce } from 'usehooks-ts';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { EmergencyContactPayLoad } from '@user/lib/models/emergency-contact';
import { HealthcareProxyContactPayLoad } from '@user/lib/models/healthcare-proxy';
import { useSearchParams } from 'react-router-dom';

import { medplumApi } from './medplum-api';
import { medplumObservationApi } from './medplum-observation';
import { useAuthService, usePublicRecordSettings, usePublicSettings } from '@lib/state';
import {
  ObservationPayload,
  PatientPayload,
  UpdatePatientPayload,
  // PatientPhotoPayload
} from './models/patient';
import {
  CareTeam,
  Condition,
  FamilyMemberHistory,
  Observation,
  Patient,
  Questionnaire,
  QuestionnaireResponse,
  RelatedPerson,
} from 'src/gql/graphql';
import { AnalyticsService } from '@lib/analyticsService';
import { filterTasksByCode, sortByTimestamp } from '@lib/utils/utils';
import { FH_CODE_SYSTEM_CREATED_AT } from 'src/constants/medplumConstants';
import {
  DeleteQuestionnaireResponseTaskPayload,
  ObservationVitalResponsePayload,
  QuestionnaireResponsePayload,
} from './models/questionnaire-response';
import { FamilyMemberHistoryPayload } from '@lib/models/family-member-history';
import { INVALIDATE_DELAY } from '@lib/constants';
import { Immunization } from '@lib/models/immunization';
import { enumMesurment, enumUnit } from './constants';
import { documentPasswordSubmission } from 'src/app/medical-records/lib/constants';
// import { useIntersected } from '@lib/utils/utils';
// import { MasterLifestyleCategory } from '@lib/models/lifestyle-and-nutrition';
// import {
// addLazyPaginatedListEntity,
// addPaginatedListEntity,
// getCachedQueryKeys,
// getNextPageParam,
// removeEntityInLazyPaginatedList,
// removeEntityInPaginatedList,
// updateEntityInPaginatedList,
// } from '@lib/utils/state-utils';

// Constants
const STATE_TYPES = {
  PATIENT: 'medplum-patient',
  PATIENT_SETTING: 'patient_setting',
  RELATED_PATIENT: 'medplum-related-patient',
  EMERGENCY_CONTACT_LIST: 'emergency_contact_list',
  HEALTHCARE_PROXY_LIST: 'healthcare_proxy_list',
  HEALTH_INSURANCE_LIST: 'health_insurance_list',
  MASTER_QUESTIONNAIRE_LIST: 'master_questionnaire_list',
  MASTER_QUESTIONNAIRE_RESPONSE_LIST: 'master_questionnaire_response_list',
  SHARE_MASTER_QUESTIONNAIRE_RESPONSE_LIST: 'share_master_questionnaire_response_list',
  SHARE_FAMILY_MEMBER_HISTORY_LIST: 'share_family_member_history_list',
  MASTER_QUESTIONNAIRE_RESPONSE_LIST_PS: 'master_questionnaire_response_list_ps',
  HEALTH_INSURANCE_COMPANY_LIST: 'health_insurance_company_list',
  CARE_TEAM: 'care_team',
  VALUE_SET: 'value_set',
  VALUE_SET_QUESTIONNAIRE: 'value_set_questionnaire',
  VALUE_SET_CARE_TEAM: 'value_set_care_team',
  VALUE_SET_MEDICATION: 'value_set_medtication',
  DOCTOR_LIST: 'doctor_list',
  SHARE_PROFILE: 'share_profile',
  RELATED_PERSON_LIST: 'related_person_list',
  CONDITION_SUGGESTIONS_LIST: 'condition_suggestions_list',
  FAMILY_MEMBER_HISTORY_LIST: 'family_member_history_list',
  RECORDS_IN_REVIEW: 'records_in_review',
  AUDIT_EVENTS_LIST: 'audit_event_list',
  PROCEDURE_LIST: 'procedure_list',
  DELETE_PATIENT: 'delete_patient',
  VITAL_LIST: 'vital_list',
  SYMPTOM_LIST: 'symptom_list',
  ALLERGIES_INTOLERANCES_LIST: 'allergies_intolerances_list',
  CONDITIONS_LIST: 'conditions_list',
  PREVENTATIVE_SCREENING: 'preventative_screen_list',
  IMMUNIZATION_LIST: 'immunization_list',
  MEDICATION_LIST: 'medication_list',
  SHARE_TIMELINE: 'share_timeline_data',
};
type SymptomListData = {
  ObservationList: Observation[];
};

type ProcedureListResponse = {
  ProcedureList: any[];
};
export const usePatient = (pId?: Patient['id']) => {
  const queryClient = useQueryClient();
  const { authenticatedUser: patient, setAuthenticatedUser } = useAuthService();
  const pubSettings = usePublicSettings();
  const pubRecordSettings = usePublicRecordSettings();
  const { isPublicMode, settings, myPatient } = pubSettings;
  const { isPublicRecordMode, myRecordPatient } = pubRecordSettings;
  const patientId =
    !isPublicMode && !isPublicRecordMode ? pId || patient?.id : isPublicMode ? myPatient?.id : myRecordPatient?.id;
  // console.log(patientId, myRecordPatient);
  const getPatient = () => {
    if (isPublicMode && settings) {
      return myPatient; // Promise.resolve(settings.patient);
    }
    if (isPublicRecordMode) {
      return myRecordPatient;
    }
    return medplumApi.patientInfo.getPatient(patientId);
  };
  const { data } = useQuery([STATE_TYPES.PATIENT, patientId], getPatient, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
    enabled: !!patientId,
    initialData: patient,
  });
  const clearPatientState = async () => {
    try {
      queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
    } catch (error) {
      console.error('Error updating patient:', error);
      throw error;
    }
  };

  const updatePatient = (payload: PatientPayload) => medplumApi.patientInfo.updatePatient(patientId, payload);
  const deletePatientData = (payload: any) => medplumApi.patientInfo.deletePatientData(patientId, payload);
  const { mutateAsync: mutateUpdatePatient } = useMutation(updatePatient, {
    onSuccess: (response, requestPayload) => {
      const updatedPatient = {
        ...patient,
        ...requestPayload,
      };

      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        AnalyticsService.instance.identifyUser(updatedPatient);
        // Keep the authentication service up to date with the patient object
        setAuthenticatedUser(updatedPatient);

        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      });
    },
  });

  const { mutateAsync: mutateDeletePatient } = useMutation(deletePatientData, {
    onSuccess: (response, requestPayload) => {
      const updatedPatient = {
        ...patient,
        ...requestPayload,
      };

      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        AnalyticsService.instance.identifyUser(updatedPatient);
        // Keep the authentication service up to date with the patient object
        setAuthenticatedUser(updatedPatient);

        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      });
    },
  });

  const updatePatientDetail = async ({
    payload,
    previousValue,
    isCleared,
  }: {
    payload: UpdatePatientPayload;
    previousValue: string;
    isCleared?: boolean;
  }) => {
    return medplumApi.patientInfo.updatePatientDetail(patientId, payload, previousValue, isCleared);
  };

  const { mutateAsync: mutateUpdatePatientDetail } = useMutation(updatePatientDetail, {
    onSuccess: async (_, { payload }) => {
      const updatedPatient = {
        ...patient,
        ...payload,
      };

      try {
        await medplumApi.patientInfo.getPatientApi(patientId);
        AnalyticsService.instance.identifyUser(updatedPatient);
        setAuthenticatedUser(updatedPatient);
        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      } catch (error) {
        console.error('Failed to fetch updated patient data:', error);
      }
    },
  });

  const addPatientObservation = (payload: ObservationPayload) =>
    medplumApi.patientInfo.addPatientObservation(patientId, payload);
  const { mutateAsync: mutateAddPatientObservation } = useMutation(addPatientObservation, {
    onSuccess: () => {
      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      });
    },
  });
  const updatePatientBloodTypeObservation = ({
    observationId,
    payload,
    previousValue,
  }: {
    observationId: Observation['id'];
    payload: any;
    previousValue: string;
  }) => medplumApi.patientInfo.updatePatientBloodTypeObservation(observationId, payload, previousValue, patientId);
  const { mutateAsync: mutateUpdatePatientBloodTypeObservation } = useMutation(updatePatientBloodTypeObservation, {
    onSuccess: () => {
      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      });
    },
  });

  const deletePatientObservation = ({
    observationId,
    previousValue,
  }: {
    observationId: Observation['id'];
    previousValue: string;
  }) => medplumApi.patientInfo.deletePatientObservation(patient, observationId, previousValue);
  const { mutateAsync: mutateDeletePatientObservation } = useMutation(deletePatientObservation, {
    onSuccess: () => {
      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.EMERGENCY_CONTACT_LIST, patientId]);
      });
    },
  });
  const updatePatientSettings = (payload: any) => medplumApi.patientInfo.updatePatientSettings(patientId, payload);
  const { mutateAsync: mutateUpdatePatientSettings } = useMutation(updatePatientSettings, {
    onSuccess: () => {
      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      });
    },
  });
  // // Todo perhaps not required
  // queryClient.setQueryData([STATE_TYPES.RELATED_PATIENT, patientId], data?.link?.map((relatedPerson: any) => relatedPerson?.other?.resource?.patient?.resource?.id));

  // const uploadProfileImage = (payload: PatientPhotoPayload) => api.patientInfo.updateProfilePhoto(patientId, payload);
  // const { mutateAsync: mutateUpdatePatientPhoto } = useMutation(uploadProfileImage, {
  //   onSuccess: async (response) => {
  //     const updatedPatient = {
  //       ...patient,
  //       photo: [response],
  //     };

  //     AnalyticsService.instance.identifyUser(updatedPatient);

  //     // Keep the authentication service up to date with the patient object
  //     setAuthenticatedUser(updatedPatient);

  //     queryClient.setQueryData([STATE_TYPES.PATIENT, patientId], updatedPatient);
  //   },
  // });

  return {
    patient: data?.data ? data?.data : data,
    updatePatient: mutateUpdatePatient,
    deletePatientData: mutateDeletePatient,
    updatePatientDetail: mutateUpdatePatientDetail,
    updatePatientPhoto: mutateUpdatePatient,
    addPatientObservation: mutateAddPatientObservation,
    updatePatientBloodTypeObservation: mutateUpdatePatientBloodTypeObservation,
    deletePatientObservation: mutateDeletePatientObservation,
    updatePatientSettings: mutateUpdatePatientSettings,
    // updatePatientPhoto: mutateUpdatePatientPhoto,
    clearPatientState,
  };
};

export const usePatientSettingsAll = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();
  const getPatientSettingsAll = () => {
    return medplumApi.patientInfo.getPatientSettingsAll(patientId);
  };
  const { data } = useQuery([STATE_TYPES.PATIENT_SETTING, patientId], getPatientSettingsAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: 0,
    cacheTime: 0,
    enabled: !!patientId,
  });
  const updateBasicSettingsCommunication = async (payload: any) => {
    return medplumApi.patientInfo.updateBasicSettingsCommunication(payload);
  };
  const updateBasicSettings = (payload: any) => {
    if (!payload?.basicId) {
      const createPayload = {
        resourceType: 'Basic',
        identifier: [
          {
            value: 'urn:fh-static:user:settings:Patient',
          },
        ],
        code: {
          coding: [
            {
              system: 'http://terminology.hl7.org/CodeSystem/basic-resource-type',
              code: 'settings',
              display: 'Settings',
            },
          ],
        },
        subject: {
          reference: `Patient/${patientId}`,
        },
        extension: [
          {
            url: 'https://fluentinhealth.com/fhir/StructureDefinition/Settings',
            extension: [
              {
                url: 'measurementPreferences',
                extension: [
                  {
                    url: 'heightUnit',
                    extension: [
                      {
                        url: 'preference',
                        valueCoding: {
                          code: payload?.path === enumMesurment.HEIGHT ? payload?.value?.code : enumUnit.INCHES,
                        },
                      },
                      {
                        url: 'visibility',
                        valueString: 'ios',
                      },
                      {
                        url: 'visibility',
                        valueString: 'android',
                      },
                      {
                        url: 'visibility',
                        valueString: 'browser',
                      },
                    ],
                  },
                  {
                    url: 'weightUnit',
                    extension: [
                      {
                        url: 'preference',
                        valueCoding: {
                          code: payload?.path === enumMesurment.WEIGHT ? payload?.value?.code : enumUnit.KG,
                        },
                      },
                      {
                        url: 'visibility',
                        valueString: 'ios',
                      },
                      {
                        url: 'visibility',
                        valueString: 'android',
                      },
                      {
                        url: 'visibility',
                        valueString: 'browser',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      };
      return medplumApi.patientInfo.updateBasicSettings(createPayload);
    }
    return medplumApi.patientInfo.updateBasicSettings(payload);
  };
  const { mutateAsync: mutateUpdateBasicSettings } = useMutation(updateBasicSettings, {
    onSuccess: () => {
      medplumApi.patientInfo.getPatientSettingsAll(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.PATIENT_SETTING, patientId]);
      });
    },
  });
  const { mutateAsync: mutateUpdateBasicSettingsCommunication } = useMutation(updateBasicSettingsCommunication, {
    onSuccess: () => {
      medplumApi.patientInfo.getPatientSettingsAll(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.PATIENT_SETTING, patientId]);
      });
    },
  });
  return {
    data: data?.BasicList?.[0],
    updateBasicSettings: mutateUpdateBasicSettings,
    updateBasicSettingsCommunication: mutateUpdateBasicSettingsCommunication,
  };
};

export const useRecordsCompleted = (patientId: Patient['id']) => {
  const getRecordsCompleted = () => {
    return medplumApi.patientInfo.getRecordsCompleted(patientId);
  };
  const { data } = useQuery([STATE_TYPES.RECORDS_IN_REVIEW, patientId], getRecordsCompleted, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: 0,
    cacheTime: 0,
    enabled: !!patientId,
  });
  const recordsInReview = filterTasksByCode({
    tasks: data,
    codeSet: documentPasswordSubmission,
    mode: 'exclude',
  });
  return {
    data: recordsInReview,
  };
};
export const useEmergencyContactList = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();
  const { isPublicMode, myEmergencyContacts } = usePublicSettings();

  const getAll: any = () => {
    if (isPublicMode) return Promise.resolve(myEmergencyContacts);
    return medplumApi.emergencyContact.getAll(patientId);
  };

  const data = !isPublicMode
    ? useQuery<RelatedPerson[]>([STATE_TYPES.EMERGENCY_CONTACT_LIST, patientId], getAll, {
        notifyOnChangeProps: ['data', 'error'],
        refetchOnWindowFocus: false,
        staleTime: Infinity,
      }).data
    : myEmergencyContacts;

  let sortedData: RelatedPerson[] = data || [];
  if (
    sortedData.length &&
    sortedData.every(
      (item) => item.meta?.tag && item.meta.tag[0]?.code && item.meta.tag[0]?.system === FH_CODE_SYSTEM_CREATED_AT
    )
  ) {
    sortedData = [...sortedData].sort((a, b) => {
      const timestampA = a.meta && a.meta.tag && a.meta.tag[0]?.code ? parseInt(a.meta.tag[0].code, 10) : 0;
      const timestampB = b.meta && b.meta.tag && b.meta.tag[0]?.code ? parseInt(b.meta.tag[0].code, 10) : 0;
      return sortByTimestamp(timestampA, timestampB);
    });
  }

  const addContact = (payload: EmergencyContactPayLoad) => medplumApi.emergencyContact.addContact(patientId, payload);
  const { mutateAsync: mutateAddContact, isLoading: isAddingContact } = useMutation(addContact, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.EMERGENCY_CONTACT_LIST, patientId]);
    },
  });

  type updateContactPayload = {
    contactId: RelatedPerson['id'];
    payload: EmergencyContactPayLoad;
  };
  const updateContact = ({ contactId, payload }: updateContactPayload) =>
    medplumApi.emergencyContact.updateContact(contactId, payload);
  const { mutateAsync: mutateUpdateContact, isLoading: isUpdating } = useMutation(updateContact, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.EMERGENCY_CONTACT_LIST, patientId]);
    },
  });

  const deleteContact = (contactId: string | number) => medplumApi.emergencyContact.deleteContact(contactId);
  const { mutateAsync: mutateDeleteContact, isLoading: isDeleting } = useMutation(deleteContact, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.EMERGENCY_CONTACT_LIST, patientId]);
    },
  });

  return {
    emergencyContactList: sortedData || [],
    addContact: mutateAddContact,
    updateContact: mutateUpdateContact,
    deleteContact: mutateDeleteContact,
    isLoading: isDeleting || isUpdating || isAddingContact,
  };
};

export const useFamilyMemberHistoryList = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();
  const { isPublicMode, myFamilyMemberHistory } = usePublicSettings();
  const getAll: any = () => {
    if (isPublicMode) return Promise.resolve(myFamilyMemberHistory);
    return medplumApi.familyMemberHistory.getFamilyMemberHistoryList(patientId);
  };

  const data = !isPublicMode
    ? useQuery<any[]>([STATE_TYPES.FAMILY_MEMBER_HISTORY_LIST, patientId], getAll, {
        notifyOnChangeProps: ['data', 'error'],
        refetchOnWindowFocus: false,
        staleTime: Infinity,
      }).data
    : myFamilyMemberHistory;

  const addFamilyMember = (payload: FamilyMemberHistoryPayload) =>
    medplumApi.familyMemberHistory.addFamilyMemberHistory(patientId, payload);
  const { mutateAsync: mutateAddFamilyMember, isLoading: isAddingFamilyMember } = useMutation(addFamilyMember, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.FAMILY_MEMBER_HISTORY_LIST, patientId]);
    },
  });

  const addFamilyMemberCondition = (payload: FamilyMemberHistoryPayload) =>
    medplumApi.familyMemberHistory.addFamilyMemberHistoryCondition(payload);
  const { mutateAsync: mutateAddFamilyMemberCondition } = useMutation(addFamilyMemberCondition, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.FAMILY_MEMBER_HISTORY_LIST, patientId]);
    },
  });

  type updateFamilyMemberPayload = {
    familyMemberId: FamilyMemberHistory['id'];
    payload: FamilyMemberHistoryPayload;
  };
  const updateFamilyMember = ({ familyMemberId, payload }: updateFamilyMemberPayload) =>
    medplumApi.familyMemberHistory.updateFamilyMemberHistory(patientId, familyMemberId, payload);
  const { mutateAsync: mutateUpdateFamilyMember, isLoading: isUpdating } = useMutation(updateFamilyMember, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.FAMILY_MEMBER_HISTORY_LIST, patientId]);
    },
  });

  type deleteFamilyMemberPayload = {
    familyMemberId: FamilyMemberHistory['id'];
  };

  const deleteFamilyMember = ({ familyMemberId }: deleteFamilyMemberPayload) =>
    medplumApi.familyMemberHistory.deleteFamilyMemberHistory(familyMemberId);
  const { mutateAsync: mutateDeleteFamilyMember, isLoading: isDeleting } = useMutation(deleteFamilyMember, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.FAMILY_MEMBER_HISTORY_LIST, patientId]);
    },
  });

  return {
    familyMemberList: data || [],
    addFamilyMember: mutateAddFamilyMember,
    updateFamilyMember: mutateUpdateFamilyMember,
    deleteFamilyMember: mutateDeleteFamilyMember,
    isLoading: isDeleting || isUpdating || isAddingFamilyMember,
    addFamilyMemberCondition: mutateAddFamilyMemberCondition,
  };
};

export const useHealthcareProxyList = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();
  const { isPublicMode, myHealthcareProxy } = usePublicSettings();

  const getAll: any = () => {
    if (isPublicMode) {
      return Promise.resolve(myHealthcareProxy);
    }
    return medplumApi.healthcareProxy.getAll(patientId);
  };

  const data = !isPublicMode
    ? useQuery<RelatedPerson[]>([STATE_TYPES.HEALTHCARE_PROXY_LIST, patientId], getAll, {
        notifyOnChangeProps: ['data', 'error'],
        refetchOnWindowFocus: false,
        staleTime: Infinity,
      }).data
    : myHealthcareProxy;

  // console.log('healthcare proxy', myHealthcareProxy, data);

  // addHealthCareProxy
  const addContact = (payload: HealthcareProxyContactPayLoad) =>
    medplumApi.healthcareProxy.addContact(patientId, payload);
  const { mutateAsync: mutateAddContact, isLoading: isAdding } = useMutation(addContact, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.HEALTHCARE_PROXY_LIST, patientId]);
    },
  });

  // updateHealthCareProxy
  type updateContactPayload = {
    contactId: RelatedPerson['id'];
    updatePayload: HealthcareProxyContactPayLoad;
  };
  const updateContact = ({ contactId, updatePayload }: updateContactPayload) =>
    medplumApi.healthcareProxy.updateContact(contactId, updatePayload);
  const { mutateAsync: mutateUpdateContact, isLoading: isUpdating } = useMutation(updateContact, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.HEALTHCARE_PROXY_LIST, patientId]);
    },
  });

  // deleteFile

  const deleteFile = (payload: HealthcareProxyContactPayLoad) => medplumApi.healthcareProxy.deleteFile(payload);
  const { mutateAsync: mutateDeleteFile, isLoading: isDeletingFile } = useMutation(deleteFile, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.HEALTHCARE_PROXY_LIST, patientId]);
    },
  });

  // deleteHealthcareProxy
  const deleteContact = (contactId: string) => medplumApi.healthcareProxy.deleteContact(contactId);
  const { mutateAsync: mutateDeleteContact, isLoading: isDeleting } = useMutation(deleteContact, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.HEALTHCARE_PROXY_LIST, patientId]);
    },
  });

  return {
    healthcareProxyList: data || [],
    addContact: mutateAddContact,
    updateContact: mutateUpdateContact,
    deleteContact: mutateDeleteContact,
    deleteFile: mutateDeleteFile,
    isLoading: isAdding || isUpdating || isDeleting,
    isDeletingFile,
  };
};

export const useHealthInsuranceList = (patientId?: Patient['id']) => {
  const queryClient = useQueryClient();
  const { isPublicMode, myHealthInsurances } = usePublicSettings();
  const getAll: any = () => {
    if (isPublicMode) return Promise.resolve(myHealthInsurances);
    return medplumApi.healthInsurance.getAll(patientId);
  };

  const data = !isPublicMode
    ? useQuery<any[]>([STATE_TYPES.HEALTH_INSURANCE_LIST, patientId], getAll, {
        notifyOnChangeProps: ['data', 'error'],
        refetchOnWindowFocus: false,
        staleTime: Infinity,
      }).data
    : myHealthInsurances;

  const addInsurance = (payload: any) => medplumApi.healthInsurance.addHealthInsurance(patientId, payload);
  const { mutateAsync: mutateAddInsurance, isLoading: isAdding } = useMutation(addInsurance, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.HEALTH_INSURANCE_LIST, patientId]);
    },
  });

  const deleteInsurance = (payload: any) => medplumApi.healthInsurance.deleteHealthInsurance(payload);
  const { mutateAsync: mutateDeleteInsurance, isLoading: isDeleting } = useMutation(deleteInsurance, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.HEALTH_INSURANCE_LIST, patientId]);
    },
  });
  const updateInsurance = (updatePayload: any) => medplumApi.healthInsurance.updateHealthInsurance(updatePayload);
  const { mutateAsync: mutateUpdateInsurance, isLoading: isUpdating } = useMutation(updateInsurance, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.HEALTH_INSURANCE_LIST, patientId]);
    },
  });

  return {
    insuranceList: data || [],
    addInsurance: mutateAddInsurance,
    deleteInsurance: mutateDeleteInsurance,
    updateInsurance: mutateUpdateInsurance,
    isLoading: isAdding || isDeleting || isUpdating,
  };
};

export const useHealthInsuranceCompanyList = (keyword: string) => {
  const getAll = () => medplumApi.healthInsurance.getAllOrganization();
  const { data } = useQuery([STATE_TYPES.HEALTH_INSURANCE_COMPANY_LIST, keyword], getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
  });
  const healthInsuranceCompanyList = data?.ValueSetList?.[0].compose?.include?.[0]?.concept || [];
  return {
    healthInsuranceCompanyList,
  };
};

// Currently only single selection and date-picker are supported.
export const REPRODUCTIVE_HEALTH_QUESTION_FIELD_TYPES = {
  choice: 'choice',
  date: 'date',
};

export const useMasterQuestionnaireList = (url: Questionnaire['url']) => {
  const { isPublicMode } = usePublicSettings();
  const [searchParams] = useSearchParams();

  const getAll = () =>
    medplumApi.masterQuestionnaire.getAllMasterQuestionnaireList(
      url,
      isPublicMode,
      searchParams.get('access_token') || undefined
    );
  const data: any = (() => {
    const qResp = useQuery<Questionnaire[]>([STATE_TYPES.MASTER_QUESTIONNAIRE_LIST, url], getAll, {
      notifyOnChangeProps: ['data', 'error'],
      refetchOnWindowFocus: false,
      staleTime: Infinity,
    });
    return qResp.data || [];
  })();

  return {
    masterList: data ?? ([] as Questionnaire[]),
  };
};
export const useMasterQuestionnaireResponseList = (
  url: Questionnaire['url'],
  patientId: Patient['id'],
  shareStateIdentifier?: string
) => {
  const queryClient = useQueryClient();
  const getAll = () => medplumApi.masterQuestionnaire.getAllMasterQuestionnaireResponseList(url, patientId);

  const pubSettings: any = usePublicSettings();
  const data = (() => {
    if (!pubSettings.isPublicMode) {
      // Fetch data when not in public mode
      const queryResult = useQuery<QuestionnaireResponse[]>(
        [STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST, url, patientId],
        getAll,
        {
          notifyOnChangeProps: ['data', 'error'],
          refetchOnWindowFocus: false,
          staleTime: Infinity,
        }
      );
      return queryResult.data || [];
    }
    // Handle public mode scenario
    if (shareStateIdentifier && shareStateIdentifier in pubSettings) {
      return pubSettings[shareStateIdentifier];
    }
    return [];
  })();
  const addQuestionnaireResponse = (payload: QuestionnaireResponsePayload) =>
    medplumApi.masterQuestionnaire.addQuestionnaireResponse(patientId, payload);
  const { mutateAsync: mutateAddQuestionnaireResponse, isLoading: isAddingContact } = useMutation(
    addQuestionnaireResponse,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST]);
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST_PS]);
        queryClient.invalidateQueries([url]);
        queryClient.invalidateQueries([patientId]);
      },
    }
  );

  const updateQuestionnaireResponse = (payload: QuestionnaireResponsePayload) =>
    medplumApi.masterQuestionnaire.updateQuestionnaireResponse(patientId, payload);
  const { mutateAsync: mutateQuestionnaireResponse, isLoading: isUpdating } = useMutation(updateQuestionnaireResponse, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST]);
      queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST_PS]);
      queryClient.invalidateQueries([url]);
      queryClient.invalidateQueries([patientId]);
    },
  });

  const patchQuestionnaireResponseStatus = (payload: { questionnaireId: string; status: string; existingData: any }) =>
    medplumApi.masterQuestionnaire.patchQuestionnaireResponseStatus(
      patientId,
      payload.questionnaireId,
      payload.status,
      payload.existingData
    );
  const { mutateAsync: mutatePatchQuestionnaireResponseStatus, isLoading: isPatching } = useMutation(
    patchQuestionnaireResponseStatus,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST]);
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST_PS]);
        queryClient.invalidateQueries([url]);
        queryClient.invalidateQueries([patientId]);
      },
    }
  );

  const updateQuestionnaireResponseAsBundleForConditionDownloadShare = (payload: any[]) =>
    medplumApi.masterQuestionnaire.updateQuestionnaireResponseAsBundleForConditionDownloadShare(patientId, payload);
  const { mutateAsync: mutateQuestionnaireResponseAsBundleForConditionDownloadShare, isLoading: isUpdating2 } =
    useMutation(updateQuestionnaireResponseAsBundleForConditionDownloadShare, {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST]);
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST_PS]);
        queryClient.invalidateQueries([url]);
        queryClient.invalidateQueries([patientId]);
      },
    });

  const deleteQuestionnaireResponse = (questionnaireResponseId: QuestionnaireResponse['id']) =>
    medplumApi.masterQuestionnaire.deleteQuestionnaireResponse(questionnaireResponseId);
  const { mutateAsync: mutateDeleteQuestionnaireResponse, isLoading: isDeleting } = useMutation(
    deleteQuestionnaireResponse,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST]);
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST_PS]);
        queryClient.invalidateQueries([url]);
        queryClient.invalidateQueries([patientId]);
      },
    }
  );

  const deleteQuestionnaireResponseTask = (payload: DeleteQuestionnaireResponseTaskPayload) =>
    medplumApi.masterQuestionnaire.deleteQuestionnaireResponseTask(payload);
  const { mutateAsync: mutateDeleteQuestionnaireResponseTask, isLoading: isDeletingTask } = useMutation(
    deleteQuestionnaireResponseTask,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST]);
        queryClient.invalidateQueries([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST_PS]);
        queryClient.invalidateQueries([url]);
        queryClient.invalidateQueries([patientId]);
      },
    }
  );

  return {
    answerList: data ?? ([] as QuestionnaireResponse[]),
    addQuestionnaireResponse: mutateAddQuestionnaireResponse,
    updateQuestionnaireResponse: mutateQuestionnaireResponse,
    patchQuestionnaireResponseStatus: mutatePatchQuestionnaireResponseStatus,
    updateQuestionnaireResponseAsBundleForConditionDownloadShare:
      mutateQuestionnaireResponseAsBundleForConditionDownloadShare,
    deleteQuestionnaireResponse: mutateDeleteQuestionnaireResponse,
    deleteQuestionnaireResponseTask: mutateDeleteQuestionnaireResponseTask,
    isLoading: isDeleting || isUpdating || isUpdating2 || isAddingContact || isDeletingTask || isPatching,
  };
};
type ImmunizationResponse = {
  ImmunizationList: Immunization[];
};

export const useImmunization = (patientId: string) => {
  const queryClient = useQueryClient();

  const getAll = () => medplumApi.immunization.getImmunizationList(patientId);

  const { data } = useQuery<ImmunizationResponse>([STATE_TYPES.IMMUNIZATION_LIST, patientId], getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
  });

  const { mutateAsync: mutateAddImmunization, isLoading: isAdding } = useMutation(
    (payload: Immunization) => medplumApi.immunization.addImmunization(payload),
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.IMMUNIZATION_LIST, patientId]);
      },
    }
  );

  const { mutateAsync: mutateUpdateImmunization, isLoading: isUpdating } = useMutation(
    (payload: Immunization) => medplumApi.immunization.updateImmunization(patientId, payload),
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.IMMUNIZATION_LIST, patientId]);
      },
    }
  );

  const { mutateAsync: mutateDeleteImmunization, isLoading: isDeleting } = useMutation(
    (immunizationId: string) => medplumApi.immunization.deleteImmunizationTask(immunizationId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.IMMUNIZATION_LIST, patientId]);
      },
    }
  );

  return {
    immunizationList: data?.ImmunizationList ?? [],
    addImmunization: mutateAddImmunization,
    updateImmunization: mutateUpdateImmunization,
    deleteImmunization: mutateDeleteImmunization,
    isLoading: isAdding || isUpdating || isDeleting,
  };
};
export const useMedication = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();

  const getAllMedications = () => medplumApi.medication.getMedicationList(patientId);
  const data =
    useQuery<any[]>([STATE_TYPES.MEDICATION_LIST, patientId], getAllMedications, {
      notifyOnChangeProps: ['data', 'error'],
      refetchOnWindowFocus: false,
      staleTime: Infinity,
    }).data || [];

  const addMedication = ({ payload }: { payload: any }) => medplumApi.medication.addMedication(patientId, payload);
  const { mutateAsync: mutateAddMedication, isLoading: isAdding } = useMutation(addMedication, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.MEDICATION_LIST]);
      queryClient.invalidateQueries([patientId]);
    },
  });

  const updateMedication = ({ payloadMedication }: { payloadMedication: any }) =>
    medplumApi.medication.updateMedication({ payloadMedication });
  const { mutateAsync: mutateUpdateMedication, isLoading: isUpdating } = useMutation(updateMedication, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.MEDICATION_LIST]);
      queryClient.invalidateQueries([patientId]);
    },
  });

  const deleteMedication = ({ medicationId, identifier }: { medicationId: any; identifier: string }) =>
    medplumApi.medication.deleteMedication(medicationId, identifier);
  const { mutateAsync: mutateDeleteMedication, isLoading: isDeleting } = useMutation(deleteMedication, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.MEDICATION_LIST, patientId]);
    },
  });

  return {
    medicationList: data,
    addMedication: mutateAddMedication,
    updateMedication: mutateUpdateMedication,
    deleteMedication: mutateDeleteMedication,
    isLoading: isAdding || isUpdating || isDeleting,
  };
};

export const useMasterConditionResponseList = (
  // url: Questionnaire['url'],
  patientId: Patient['id']
) => {
  const queryClient = useQueryClient();
  // const { myConditions } = usePublicSettings();
  // const getAll = () => medplumApi.masterQuestionnaire.getAllMasterConditionList(patientId);
  const getAll = () => {
    // console.log('data', )
    return medplumApi.masterQuestionnaire.getAllMasterConditionList(patientId);
  };

  const data = (() => {
    const queryResult = useQuery<any[]>([STATE_TYPES.CONDITIONS_LIST, patientId], getAll, {
      notifyOnChangeProps: ['data', 'error'],
      refetchOnWindowFocus: false,
      staleTime: Infinity,
    });
    return queryResult.data || [];
  })();

  const addConditions = (payload: Condition) => medplumApi.masterQuestionnaire.addConditions(patientId, payload);
  const { mutateAsync: mutateAddConditions, isLoading: isAddingContact } = useMutation(addConditions, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.CONDITIONS_LIST]);
      queryClient.invalidateQueries([patientId]);
    },
  });

  const updateConditions = ({ payloadCondition, conditionId }: { payloadCondition: Condition; conditionId: string }) =>
    medplumApi.masterQuestionnaire.updateConditions(patientId, payloadCondition, conditionId);
  const { mutateAsync: mutateConditions, isLoading: isUpdating } = useMutation(updateConditions, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.CONDITIONS_LIST]);
      queryClient.invalidateQueries([patientId]);
    },
  });

  const updateQuestionnaireResponseAsBundleForConditionDownloadShare = (payload: any[]) =>
    medplumApi.masterQuestionnaire.updateQuestionnaireResponseAsBundleForConditionDownloadShare(patientId, payload);
  const { mutateAsync: mutateQuestionnaireResponseAsBundleForConditionDownloadShare, isLoading: isUpdating2 } =
    useMutation(updateQuestionnaireResponseAsBundleForConditionDownloadShare, {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.CONDITIONS_LIST]);
        queryClient.invalidateQueries([patientId]);
      },
    });

  const deleteConditions = (conditionId: Condition['id']) =>
    medplumApi.masterQuestionnaire.deleteCondition(conditionId);
  const { mutateAsync: mutateDeleteConditions, isLoading: isDeleting } = useMutation(deleteConditions, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.CONDITIONS_LIST, patientId]);
    },
  });

  const deleteConditionsTask = (payload: DeleteQuestionnaireResponseTaskPayload) =>
    medplumApi.masterQuestionnaire.deleteQuestionnaireResponseTask(payload);
  const { mutateAsync: mutateDeleteConditionsTask, isLoading: isDeletingTask } = useMutation(deleteConditionsTask, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.CONDITIONS_LIST]);
      queryClient.invalidateQueries([patientId]);
    },
  });

  return {
    answerList: data ?? ([] as any),
    addConditions: mutateAddConditions,
    updateConditions: mutateConditions,
    updateQuestionnaireResponseAsBundleForConditionDownloadShare:
      mutateQuestionnaireResponseAsBundleForConditionDownloadShare,
    deleteConditions: mutateDeleteConditions,
    deleteConditionsTask: mutateDeleteConditionsTask,
    isLoading: isDeleting || isUpdating || isUpdating2 || isAddingContact || isDeletingTask,
  };
};

export const useMasterCommunicationResponseList = (patientId: Patient['id']) => {
  const addCommunicationRequest = (payload: any) => {
    if (!patientId) {
      throw new Error('patientId is required');
    }
    return medplumApi.communicationRequest.addCommunicationRequest(patientId as string, payload);
  };
  const { mutateAsync: mutateAddCommunicationRequest } = useMutation(addCommunicationRequest);
  return {
    addCommunicationRequest: mutateAddCommunicationRequest,
  };
};
export const useMasterQuestionnaireResponsePSList = (patientId: Patient['id']) => {
  const { isPublicMode } = usePublicSettings();

  const getAll = () => medplumApi.masterQuestionnaire.getAllMasterQuestionnaireResponsePSList(patientId);
  const data = (() => {
    if (!isPublicMode) {
      const queryResult = useQuery<QuestionnaireResponse[]>(
        [STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST_PS, patientId],
        getAll,
        {
          notifyOnChangeProps: ['data', 'error'],
          refetchOnWindowFocus: false,
          staleTime: Infinity,
        }
      );

      return queryResult.data || [];
    }
    return [];
  })();
  return {
    answerList: data as any,
  };
};
export const useMasterObservationResponseList = (
  patientId: Patient['id'],
  url?: any,
  shareStateIdentifier?: string
) => {
  const queryClient = useQueryClient();
  const pubSettings: any = usePublicSettings();

  // const get = () => medplumObservationApi.masterVitalObservation.getVitalObservationResponseList(url, patientId);
  const get = async () => ({
    [url!]: await medplumObservationApi.masterVitalObservation.getVitalObservationResponseList(url, patientId),
  });

  const getAll = async () => {
    const urls = [
      'vi:blood-pressure',
      'vi:oxygen-saturation-level',
      'vi:pulse-rate',
      'vi:respiratory-rate',
      'vi:body-temperature',
    ];
    try {
      const responses = await Promise.all(
        urls.map((vitalUrl) =>
          medplumObservationApi.masterVitalObservation
            .getVitalObservationResponseList(vitalUrl, patientId)
            .then((data) => ({ [vitalUrl]: data }))
        )
      );

      return responses.reduce((acc, response) => ({ ...acc, ...response }), {});
    } catch (error) {
      console.error('Error fetching all vital observations:', error);
      throw error;
    }
  };

  const data = (() => {
    if (!pubSettings.isPublicMode && url) {
      const queryResult = useQuery<Record<string, Observation[]>>([STATE_TYPES.VITAL_LIST, url, patientId], get, {
        notifyOnChangeProps: ['data', 'error'],
        refetchOnWindowFocus: false,
        staleTime: Infinity,
      });
      return queryResult.data || {};
    }
    if (shareStateIdentifier && shareStateIdentifier in pubSettings) {
      return pubSettings[shareStateIdentifier];
    }
    return {};
  })();

  const masterVitalList = (() => {
    const queryResult = useQuery<Record<string, Observation[]>>([STATE_TYPES.VITAL_LIST, patientId], getAll, {
      notifyOnChangeProps: ['data', 'error'],
      refetchOnWindowFocus: false,
      staleTime: Infinity,
    });
    return queryResult.data || {};
  })();

  const addObservationResponse = (payload: ObservationVitalResponsePayload) =>
    medplumObservationApi.masterVitalObservation.addVitalObservation(patientId, payload);
  const { mutateAsync: mutateAddObservationResponse, isLoading: isAddingContact } = useMutation(
    addObservationResponse,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.VITAL_LIST]);
        queryClient.invalidateQueries([url]);
        queryClient.invalidateQueries([patientId]);
      },
    }
  );

  const updateObservationResponse = (payload: ObservationVitalResponsePayload) =>
    medplumObservationApi.masterVitalObservation.updateVitalObservation(patientId, payload);
  const { mutateAsync: mutateUpdateObservationResponse, isLoading: isUpdating } = useMutation(
    updateObservationResponse,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.VITAL_LIST]);
        queryClient.invalidateQueries([url]);
        queryClient.invalidateQueries([patientId]);
      },
    }
  );

  const deleteObservationResponseTask = (payload: ObservationVitalResponsePayload) =>
    medplumObservationApi.masterVitalObservation.deleteVitalObservationTask(payload);
  const { mutateAsync: mutateDeleteObservationResponseTask, isLoading: isDeletingTask } = useMutation(
    deleteObservationResponseTask,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.VITAL_LIST]);
        queryClient.invalidateQueries([url]);
        queryClient.invalidateQueries([patientId]);
      },
    }
  );

  return {
    answerList: data ?? ([] as Observation[]),
    masterVitalList: masterVitalList ?? ({} as Record<string, Observation[]>),
    addObservationResponse: mutateAddObservationResponse,
    updateObservationResponse: mutateUpdateObservationResponse,
    deleteObservationResponseTask: mutateDeleteObservationResponseTask,
    isLoading: isDeletingTask || isUpdating || isAddingContact || isDeletingTask,
  };
};

export const useVaccineQuestionnaireResponseList = (patientId: Patient['id']) => {
  const pubSettings = usePublicSettings();
  const {
    isPublicMode,
    myImmunizationCovid19,
    myImmunizationFlu,
    myImmunizationVaricella,
    myImmunizationTuberculosis,
    myImmunizationHepatitisB,
    myImmunizationHepatitisA,
    myImmunizationPneumonia,
    myImmunizationMeaslesMumpsRubellaMMR,
    myImmunizationTDap,
    myImmunizationShingles,
    myImmunizationHPV,
  } = pubSettings;
  const getAll = () => medplumApi.vaccineQuestionnaire.getAllVaccineQuestionnaireResponseList(patientId);
  const data = (() => {
    if (!isPublicMode) {
      const queryResult = useQuery([STATE_TYPES.MASTER_QUESTIONNAIRE_RESPONSE_LIST, patientId], getAll, {
        notifyOnChangeProps: ['data', 'error'],
        refetchOnWindowFocus: false,
        staleTime: Infinity,
      });

      return (
        queryResult.data || {
          ImmunizationCovid19: myImmunizationCovid19,
          ImmunizationFlu: myImmunizationFlu,
          ImmunizationVaricella: myImmunizationVaricella,
          ImmunizationTuberculosis: myImmunizationTuberculosis,
          ImmunizationHepatitisB: myImmunizationHepatitisB,
          ImmunizationHepatitisA: myImmunizationHepatitisA,
          ImmunizationPneumonia: myImmunizationPneumonia,
          ImmunizationMeaslesMumpsRubellaMMR: myImmunizationMeaslesMumpsRubellaMMR,
          ImmunizationTDap: myImmunizationTDap,
          ImmunizationShingles: myImmunizationShingles,
          ImmunizationHPV: myImmunizationHPV,
        }
      );
    }
    return {};
  })();

  return {
    answerList: data as any,
  };
};
export const useCareTeamList = (patientId: Patient['id']) => {
  const { isPublicMode, myCareTeam } = usePublicSettings();

  const getAll: any = () => {
    if (isPublicMode) return Promise.resolve(myCareTeam);
    return medplumApi.careTeam.getAll(patientId);
  };
  const { data, isLoading } = useQuery([STATE_TYPES.CARE_TEAM, patientId], getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
  });
  const queryClient = useQueryClient();

  const addMember = async ({ formValues }: { formValues: any }) => {
    return medplumApi.careTeam.create(patientId, formValues);
  };
  const editMember = async ({ formValues, subMember }: { formValues: any; subMember?: any }) => {
    return medplumApi.careTeam.edit(patientId, { ...formValues, subMember });
  };
  const removeMember = async ({ formValues, careTeamMemberList }: { formValues: any; careTeamMemberList: any }) => {
    return medplumApi.careTeam.remove(patientId, formValues, careTeamMemberList);
  };
  const addNonFluentMenber = async ({
    formValues,
    careTeamMemberList,
  }: {
    formValues: any;
    careTeamMemberList: any;
  }) => {
    return medplumApi.careTeam.addNonFluent(patientId, formValues, careTeamMemberList);
  };

  const editNonFluentMenber = async ({
    formValues,
    practitionerId,
    practitionerRoleId,
    careTeamMemberList,
  }: {
    formValues: any;
    practitionerId?: string;
    practitionerRoleId?: string;
    careTeamMemberList?: any;
  }) => {
    return medplumApi.careTeam.editNonFluent(patientId, {
      formValues,
      practitionerId,
      practitionerRoleId,
      careTeamMemberList,
    });
  };

  const { mutateAsync: mutateAddMember } = useMutation(addMember, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.CARE_TEAM]);
      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      });
    },
  });
  const { mutateAsync: mutateEditMember } = useMutation(editMember, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.CARE_TEAM]);
      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      });
    },
  });
  const { mutateAsync: mutateRemoveMember } = useMutation(removeMember, {
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([STATE_TYPES.CARE_TEAM]);
      }, INVALIDATE_DELAY);
    },
  });
  const { mutateAsync: mutateAddNonMember } = useMutation(addNonFluentMenber, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.CARE_TEAM]);
      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      });
    },
  });
  const { mutateAsync: mutateEditNonMember } = useMutation(editNonFluentMenber, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.CARE_TEAM]);
      medplumApi.patientInfo.getPatientApi(patientId).then(() => {
        queryClient.invalidateQueries([STATE_TYPES.PATIENT, patientId]);
      });
    },
  });
  return {
    careTeamMemberList: data as CareTeam[],
    addMember: mutateAddMember,
    editMember: mutateEditMember,
    removeMember: mutateRemoveMember,
    addNonFluentMenber: mutateAddNonMember,
    editNonFluentMenber: mutateEditNonMember,
    isLoading,
  };
};

export const useValueSetQuestionnaire = (urls: string[]) => {
  const { isPublicMode } = usePublicSettings();
  const [searchParams] = useSearchParams();
  if (urls) {
    const getAll = () =>
      medplumApi.valueSetList.getAllQuestionnaireList(urls, isPublicMode, searchParams.get('access_token'));
    const { data } = useQuery([STATE_TYPES.VALUE_SET_QUESTIONNAIRE, urls], getAll, {
      notifyOnChangeProps: ['data', 'error'],
      refetchOnWindowFocus: false,
      staleTime: Infinity,
    });
    return data as [];
  }
  return [];
};
// export const useValueSetMedication = (searchText: string) => {
//   const { isPublicMode } = usePublicSettings();
//   const [searchParams] = useSearchParams();
//   const getAll = () => medplumApi.valueSetList.getAllValueSetMedication(isPublicMode, searchText,searchParams.get('access_token'));
//   const { data } = useQuery([STATE_TYPES.VALUE_SET_MEDICATION], getAll, {
//     notifyOnChangeProps: ['data', 'error'],
//     refetchOnWindowFocus: false,
//     staleTime: 0,
//   });

//   return data as [];
// };
export const useValueSetCareTeam = () => {
  const { isPublicMode } = usePublicSettings();
  const [searchParams] = useSearchParams();
  const getAll = () => {
    if (isPublicMode) return [];
    return medplumApi.valueSet.getValueSetCareTeam(isPublicMode, searchParams.get('access_token'));
  };
  const { data } = useQuery([STATE_TYPES.VALUE_SET_CARE_TEAM], getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
  });

  return {
    valueSetList: data?.data ?? [],
  };
};

export const useDoctorsList = (name: string) => {
  const getAll = () => medplumApi.doctorList.getAll(name);

  const {
    data: { data },
  } = useQuery([STATE_TYPES.DOCTOR_LIST, name], getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
  });

  // Concatenate all data pages into a single array
  const doctorList = data?.PractitionerList;
  return {
    doctorList,
  };
};

export const useShareProfileRecord = (patientId: Patient['id']) => {
  const createTaskForShare = (payload: { type: string; daysToExpire: string; data: any }) =>
    medplumApi.shareProfileRecord.createTaskForShare(patientId, payload);
  const { mutateAsync: mutateCreateTaskForShare, isLoading: isAddingTask } = useMutation(createTaskForShare, {
    onSuccess: () => {},
  });

  return {
    createTaskForShare: mutateCreateTaskForShare,
    isLoading: isAddingTask,
  };
};

// export const useRelatedPersonList = (patientId: Pick<EmergencyContact, 'id'>) => {
//   const queryClient = useQueryClient();
//   const { isPublicMode, myRelatedPersons } = usePublicSettings();

//   const queryKey = [STATE_TYPES.RELATED_PERSON_LIST, patientId];
//   const getAll = ({ pageParam = 1 }) => {
//     if (isPublicMode) return Promise.resolve(myRelatedPersons);

//     return medplumApi.relatedPerson.getAll(patientId, {
//       page: pageParam,
//       limit: 4,
//     });
//   };

//   const { data, fetchNextPage, hasNextPage, isLoading, isFetching, isFetchingNextPage } = useInfiniteQuery<
//     PaginatedResponse<RelatedPerson>
//   >(queryKey, getAll, {
//     getNextPageParam: (lastPage: any) =>
//       lastPage.current_page < lastPage.last_page ? lastPage.current_page + 1 : undefined,
//     notifyOnChangeProps: ['data', 'error'],
//     refetchOnWindowFocus: false,
//     staleTime: 60000,
//   });

//   const addPerson = (payload: RelatedPersonPayload) => medplumApi.relatedPerson.addPerson(patientId, payload);
//   const { mutateAsync: mutateAddPerson, isLoading: isAddingPerson } = useMutation(addPerson, {
//     onSuccess: (person) => {
//       addLazyPaginatedListEntity(queryClient, queryKey, person);
//     },
//   });

//   type UpdatePersonPayload = {
//     personId: string | number;
//     payload: RelatedPersonPayload;
//   };

//   const updatePerson = ({ personId, payload }: UpdatePersonPayload) =>
//     medplumApi.relatedPerson.updatePerson(patientId, personId, payload);

//   const { mutateAsync: mutateUpdatePerson } = useMutation(updatePerson, {
//     onSuccess: () => {
//       queryClient.invalidateQueries(queryKey);
//     },
//   });

//   const deletePerson = (personId: string | number) => medplumApi.relatedPerson.deletePerson(patientId, personId);
//   const { mutateAsync: mutateDeletePerson, isLoading: isDeletingPerson } = useMutation(deletePerson, {
//     onSuccess: (_, personId) => {
//       removeEntityInLazyPaginatedList(queryClient, queryKey, personId);
//     },
//   });

//   // Concatenate all data pages into a single array
//   const relatedPersonList = data?.pages?.flatMap((page: any) => page?.data || []) || [];

//   return {
//     relatedPersonList,
//     addPerson: mutateAddPerson,
//     updatePerson: mutateUpdatePerson,
//     deletePerson: mutateDeletePerson,
//     fetchNextPage,
//     hasNextPage,
//     isLoading: isLoading || isAddingPerson || isDeletingPerson,
//     isFetching,
//     isFetchingNextPage,
//   };
// };

// export const useConditionSuggestionList = (params?: BaseSearchParams) => {
//   const conditionSuggestions = ({ pageParam = 1 }) =>
//     medplumApi.condition.conditionSuggestions({
//       ...params,
//       page: pageParam,
//     });
//   const { data, fetchNextPage, hasNextPage, isFetching, isFetchingNextPage } = useInfiniteQuery<
//     PaginatedResponse<ConditionSuggestion>
//   >([STATE_TYPES.CONDITION_SUGGESTIONS_LIST, params], conditionSuggestions, {
//     getNextPageParam,
//     notifyOnChangeProps: ['data', 'error'],
//     refetchOnWindowFocus: false,
//     staleTime: 60000,
//     suspense: false,
//   });

//   // Concatenate all data pages into a single array
//   const suggestionList = data?.pages?.flatMap((page) => page?.data || []) || [];

//   return {
//     suggestionList,
//     // Lazy pagination props
//     fetchNextPage,
//     hasNextPage,
//     isFetching,
//     isFetchingNextPage,
//   };
// };

export const useAuditEventList = (patientId: string) => {
  // Define getAll as an async function that returns a value
  const getAll = async () => {
    try {
      const response = await medplumApi.auditEventList.getAll(patientId);
      return response || []; // Ensure it returns an array, even if response is undefined
    } catch (error) {
      console.error('Error fetching audit events:', error);
      return []; // Return empty array on error
    }
  };

  // Use useQuery with the query function
  const { data, error } = useQuery([STATE_TYPES.AUDIT_EVENTS_LIST, patientId], getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: 0,
  });

  // Log errors if any
  if (error) {
    console.error('Error fetching audit event list:', error);
  }

  // Default data to an empty array if undefined
  const auditEventList = data || [];
  return {
    auditEventList,
  };
};

export const useProcedureList = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();

  const getAllProcedureList = (): Promise<ProcedureListResponse> => {
    return medplumApi.procedureList.getProcedureList(patientId);
  };

  const { data } = useQuery<ProcedureListResponse>([STATE_TYPES.PROCEDURE_LIST, patientId], getAllProcedureList, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
  });

  const addSurgeryProcedure = (payload: any) => medplumApi.procedureList.addSurgeryProcedure(payload);
  const { mutateAsync: mutateAddSurgeryProcedure, isLoading: isAddingSurgeryProcedure } = useMutation(
    addSurgeryProcedure,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.PROCEDURE_LIST]);
      },
    }
  );

  const updateSurgeryProcedure = (payload: any) => medplumApi.procedureList.updateSurgeryProcedure(payload);
  const { mutateAsync: mutateUpdateSurgeryProcedure, isLoading: isUpdatingSurgeryProcedure } = useMutation(
    updateSurgeryProcedure,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.PROCEDURE_LIST]);
      },
    }
  );

  const deleteSurgeryProcedureTask = (payload: any) => medplumApi.procedureList.deleteSurgeryProcedureTask(payload);
  const { mutateAsync: mutateSurgeryProcedureTask, isLoading: isDeletingTask } = useMutation(
    deleteSurgeryProcedureTask,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.PROCEDURE_LIST, patientId]);
      },
    }
  );

  return {
    procedureList: data?.ProcedureList ?? [],
    isLoading: isAddingSurgeryProcedure || isUpdatingSurgeryProcedure || isDeletingTask,
    addSurgeryProcedure: mutateAddSurgeryProcedure,
    updateSurgeryProcedure: mutateUpdateSurgeryProcedure,
    deleteSurgeryProcedureTask: mutateSurgeryProcedureTask,
  };
};

export const useDeleteProfileState = () => {
  const queryClient = useQueryClient();
  const deleteProfileTaskCreate = (payload: any) => medplumApi.deleteProfile.deleteProfileData(payload);
  const { mutateAsync: mutateDeleteProfile, isLoading: isDeleting } = useMutation(deleteProfileTaskCreate, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.DELETE_PATIENT]);
    },
  });

  // const getTaskByIdentifier = async () => {
  //   try {
  //     const response = await medplumApi.deleteProfile.getTaskCountByIdentifier(patientId);
  //     return response || []; // Ensure it returns an array, even if response is undefined
  //   } catch (error) {
  //     console.error('Error fetching audit events:', error);
  //     return []; // Return empty array on error
  //   }
  // };
  // const { data, error } = useQuery([STATE_TYPES.DELETE_PATIENT, patientId], getTaskByIdentifier, {
  //   notifyOnChangeProps: ['data', 'error'],
  //   refetchOnWindowFocus: false,
  //   staleTime: Infinity,
  // });

  // if (error) {
  //   console.error('Error fetching audit event list:', error);
  // }
  // // Default data to an empty array if undefined
  // const getTaskByIdentifierList = data || [];
  return {
    deleteProfileTaskCreate: mutateDeleteProfile,
    // getTaskByIdentifierList,
    isDeleting,
  };
};

export const useSymptomsList = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();
  const { isPublicMode } = usePublicSettings();
  const [searchParams] = useSearchParams();

  const getAll = (): Promise<SymptomListData> =>
    medplumApi.symptomsObservation.getSymptomList(
      patientId,
      'FACT-sym',
      isPublicMode,
      searchParams.get('access_token') || undefined
    );

  const { data } = useQuery<SymptomListData>([STATE_TYPES.SYMPTOM_LIST, patientId], getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
  });

  const addSymptoms = async (payload: any) => medplumApi.symptomsObservation.addSymptoms(payload);
  const { mutateAsync: mutateAddSymptom, isLoading: isAddingSymptom } = useMutation(addSymptoms, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.SYMPTOM_LIST]);
    },
  });

  const updateSymptoms = (payload: any) => medplumApi.symptomsObservation.updateSymptoms(payload);
  const { mutateAsync: mutateUpdateSymptom, isLoading: isUpdating } = useMutation(updateSymptoms, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.SYMPTOM_LIST]);
    },
  });

  type deleteSymptomPayload = {
    symptomId: any['id'];
  };

  const deleteSymptom = ({ symptomId }: deleteSymptomPayload) =>
    medplumApi.symptomsObservation.deleteSymptom(symptomId);
  const { mutateAsync: mutateDeleteSymptom, isLoading: isDeleting } = useMutation(deleteSymptom, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.SYMPTOM_LIST, patientId]);
    },
  });

  return {
    symptomListOfData: data ?? ({ ObservationList: [] } as SymptomListData),
    addSymptoms: mutateAddSymptom,
    updateSymptoms: mutateUpdateSymptom,
    deleteSymptom: mutateDeleteSymptom,
    isLoading: isUpdating || isAddingSymptom || isDeleting,
  };
};

export const useAllergiesIntolerancesList = (patientId: Patient['id']) => {
  const queryClient = useQueryClient();
  const { isPublicMode, myAllergyIntolerance } = usePublicSettings();
  const getAll: any = () => {
    if (isPublicMode) return Promise.resolve(myAllergyIntolerance);
    return medplumApi.allergyIntolerance.getAllergyIntoleranceList(patientId);
  };

  const data = !isPublicMode
    ? useQuery<any[]>([STATE_TYPES.ALLERGIES_INTOLERANCES_LIST, patientId], getAll, {
        notifyOnChangeProps: ['data', 'error'],
        refetchOnWindowFocus: false,
        staleTime: Infinity,
      }).data
    : myAllergyIntolerance;

  const addAllergiesIntolerances = async (payload: any) =>
    medplumApi.allergyIntolerance.addAllergiesIntolerances(payload);
  const { mutateAsync: mutateAddAllergyIntolerance, isLoading: isAddingAllergyIntolerance } = useMutation(
    addAllergiesIntolerances,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.ALLERGIES_INTOLERANCES_LIST]);
      },
    }
  );

  const updateAllergiesIntolerances = ({ allergyId, payload }: { allergyId: string; payload: any }) =>
    medplumApi.allergyIntolerance.updateAllergiesIntolerances({ allergyId, payloadAllergyIntolerance: payload });
  const { mutateAsync: mutateUpdateAllergyIntolerance, isLoading: isUpdating } = useMutation(
    updateAllergiesIntolerances,
    {
      onSuccess: () => {
        queryClient.invalidateQueries([STATE_TYPES.ALLERGIES_INTOLERANCES_LIST]);
      },
    }
  );

  type deleteAllergyIntolerancePayload = {
    symptomId: any['id'];
  };

  const deleteAllergyIntolerance = ({ symptomId }: deleteAllergyIntolerancePayload) =>
    medplumApi.allergyIntolerance.deleteAllergyIntolerance(symptomId);
  const { mutateAsync: mutateDeleteAllergyIntolerance, isLoading: isDeleting } = useMutation(deleteAllergyIntolerance, {
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([STATE_TYPES.ALLERGIES_INTOLERANCES_LIST, patientId]);
      }, INVALIDATE_DELAY);
    },
  });

  return {
    allergyListOfData: data?.AllergyIntoleranceList || [],
    addAllergiesIntolerances: mutateAddAllergyIntolerance,
    updateAllergiesIntolerances: mutateUpdateAllergyIntolerance,
    deleteAllergyIntolerance: mutateDeleteAllergyIntolerance,
    isLoading: isUpdating || isAddingAllergyIntolerance || isDeleting,
  };
};

export const useICD10System = () => {
  const getICD10SystemValueSet = async (payload: any) => medplumApi.icd10System.getICD10SystemValue(payload);

  const { mutateAsync: mutateGetICD10SystemValueSet, isLoading: isSettingICD10 } = useMutation(
    getICD10SystemValueSet,
    {}
  );
  return {
    getICD10SystemValue: mutateGetICD10SystemValueSet,
    isLoading: isSettingICD10,
  };
};

export const useGetValueSetForProfile = (urls: { type: string; url: string }[]) => {
  const getValueSetForProfile = (valuesetUrls: { type: string; url: string }[]) =>
    medplumApi.valueSetsForProfile.getValueSetsForProfile(valuesetUrls);
  const { data, error } = useQuery(['STATE_TYPES', urls], () => getValueSetForProfile(urls), {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
  });

  return {
    valueSetListForProfile: data ?? [],
    error,
  };
};

export const useGetPrevenativeScreening = (patientId: string, identifier: string[], count?: number) => {
  const getPreventativeScreeningPatient = async () => {
    return medplumApi.preventativeScreening.getPreventativeScreeningPatient(patientId, identifier, count);
  };
  const { data, refetch } = useQuery(
    [STATE_TYPES.PREVENTATIVE_SCREENING, patientId, identifier, count],
    getPreventativeScreeningPatient,
    {
      notifyOnChangeProps: ['data', 'error'],
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      enabled: true,
    }
  );

  return {
    preventativeScreeningList: data || [],
    getPreventativeScreening: refetch,
  };
};

// ##2 create a state here to get the procedures

export const usePreventativeScreening = (patientId: string) => {
  const queryClient = useQueryClient();

  // TypeScript-safe access to API methods
  const api = medplumApi.preventativeScreening as any;

  const addPreventativeScreening = async (payload: any) => api.addPreventativeScreening(payload);

  const {
    mutateAsync: mutateAddPreventativeScreening,
    isLoading: isAddingPreventativeScreening,
    isSuccess: isSuccessAdd,
  } = useMutation(addPreventativeScreening, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.PREVENTATIVE_SCREENING, patientId]);
    },
  });

  const updatePreventativeScreen = async ({ procedureId, payload }: { procedureId: string; payload: any }) =>
    api.updatePreventativeScreen({ procedureId, payload });

  const {
    mutateAsync: mutateUpdatePreventativeScreen,
    isLoading: isUpdatingPreventativeScreen,
    isSuccess: isSuccessUpdate,
  } = useMutation(updatePreventativeScreen, {
    onSuccess: () => {
      queryClient.invalidateQueries([STATE_TYPES.PREVENTATIVE_SCREENING, patientId]);
    },
  });

  const deletePreventativeScreening = async (screeningId: string) => api.deletePreventativeScreening(screeningId);

  const { mutateAsync: mutateDeletePreventativeScreening } = useMutation(deletePreventativeScreening, {
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries([STATE_TYPES.PREVENTATIVE_SCREENING, patientId]);
      }, INVALIDATE_DELAY);
    },
  });

  return {
    addPreventativeScreening: mutateAddPreventativeScreening,
    updatePreventativeScreening: mutateUpdatePreventativeScreen,
    isSuccessUpdate,
    isSuccessAdd,
    deletePreventativeScreening: mutateDeletePreventativeScreening,
    isLoading: isAddingPreventativeScreening || isUpdatingPreventativeScreen,
  };
};

/**
 * Consolidated Share Timeline Data hook
 */
export const useGetShareTimelineData = (patientId: Patient['id'], count?: number) => {
  const { isPublicMode } = usePublicSettings();
  const [searchParams] = useSearchParams();

  const getAll = () =>
    medplumApi.shareTimeline.getShareTimelineData(
      patientId!,
      1000,
      isPublicMode,
      searchParams.get('access_token') || undefined
    );
  const { data } = useQuery([STATE_TYPES.SHARE_TIMELINE, patientId, count], getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
    enabled: !!patientId,
  });
  return {
    shareTimelineData: data ?? {},
  };
};
export const useGetShareSnapshotData = (patientId: Patient['id']) => {
  const { isPublicMode } = usePublicSettings();
  const [searchParams] = useSearchParams();

  const getAll = () => medplumApi.shareSnapshot(patientId, isPublicMode, searchParams.get('access_token') || undefined);
  const { data } = useQuery([STATE_TYPES.SHARE_TIMELINE, patientId], getAll, {
    notifyOnChangeProps: ['data', 'error'],
    refetchOnWindowFocus: false,
    staleTime: Infinity,
    enabled: !!patientId,
  });
  return data ?? {};
};
export const useShareFamilyMemberHistoryList = (patientId: Patient['id']) => {
  const getAll = () => medplumApi.shareProfileRecord.getShareFamilyMemberHistoryList(patientId);

  const data = (() => {
    // Fetch data when not in public mode
    const queryResult = useQuery([STATE_TYPES.SHARE_FAMILY_MEMBER_HISTORY_LIST, patientId], getAll, {
      notifyOnChangeProps: ['data', 'error'],
      refetchOnWindowFocus: false,
      staleTime: Infinity,
    });
    return queryResult.data || [];
  })();

  return data?.FamilyMemberHistoryList;
};
