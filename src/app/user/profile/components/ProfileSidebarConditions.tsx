/* eslint-disable react/jsx-no-useless-fragment */
import React, { PropsWithChildren, Suspense, useEffect, useState } from 'react';
import { Container, Flex, Spacer, Stack, useDisclosure, useToast } from '@chakra-ui/react';
import { Edit3, <PERSON>O<PERSON>, Trash as TrashIcon } from 'react-feather';
import { recordConditionEvents } from '@user/lib/events-analytics-manager';
import { useNavigate } from 'react-router-dom';
import { medplumApi } from '@user/lib/medplum-api';
import { NavigationHelper, PATIENT_DEFINED, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import dayjs from 'dayjs';

import { ConditionShareProfile, ConditionType } from '@lib/models/condition';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { MODAL_VARIANTS, Modal, ModalProvider, useModal } from '../../../../components/Modal';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from '../../../../components/ui/Menu';
import { FACT_CODE_SYSTEM, deleteIdentifier } from '@lib/constants';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from './ConsentModal';
import ConditionForm, { createCondition } from './ProfileConditionForm';
import { LinkedDocumentsCard, LinkedDocumentsLabel } from './LinkedDocumentsCard';
import { FormSkeleton } from 'src/components/ui/Form';
import {
  Card,
  CardDescription,
  CardHeading,
  CardPerformedLabel,
  InheritedFromLabel,
  SidebarAddButton,
  SidebarCloseButton,
  SidebarHeading,
  SidebarHelperTooltip,
} from './SidebarComponents';
import { RoundedTab, RoundedTabList, RoundedTabPanel, RoundedTabPanels, RoundedTabs } from './RoundedTabs';
import { ISidebarProps } from '@lib/models/misc';
import { SidebarEmptyState } from './SidebarEmptyState';
import { EmptyStateTabsCard } from './EmptyStateTabsCard';
import { HideFromSharingCard } from './HideFromSharingCard';
import { useMasterConditionResponseList } from '../../lib/medplum-state';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';

function ConditionCard({
  title,
  notes,
  diagnosedDate,
  children,
  onEdit,
  onRemove,
  isShareable,
  inheritedFrom,
  onHideFromSharing,
  isPublicMode = false,
  isLoading,
  isCustomEntry,
}: PropsWithChildren<{
  title: string;
  notes: string;
  diagnosedDate: string | null;
  onEdit?: () => void;
  onRemove?: () => void;
  isShareable?: boolean;
  inheritedFrom?: string;
  onHideFromSharing?: () => void;
  isPublicMode?: boolean;
  isLoading?: boolean;
  isCustomEntry?: boolean;
}>) {
  const hideModal = useDisclosure();
  const deleteModal = useDisclosure();
  // const { trackEventInFlow } = useAnalyticsService();
  const [loader, setLoader] = useState(false);
  const onHideFromSharingHandler = () => {
    onHideFromSharing?.();
    hideModal.onClose();
  };

  return (
    <>
      <ConsentModal {...hideModal}>
        <ConsentModalHeading>
          Hiding conditions may
          <br />
          impact diagnosis
        </ConsentModalHeading>
        <ConsentModalContent>
          You chose to hide some conditions from your Health Profile, which may be important for your consult with your
          doctor. Are you sure you want to continue?
        </ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton onClick={hideModal.onClose}>Cancel</ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton
            isLoading={isLoading}
            onClick={onHideFromSharingHandler}
          >
            Continue
          </ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={() => {
              onRemove?.();
              setLoader(true);
            }}
            isLoading={isLoading || loader}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Card mt="4">
        <Flex
          gap="20px"
          direction="column"
        >
          <Flex
            justifyContent="space-between"
            color="fluentHealthText.100"
          >
            <CardHeading maxWidth="90%">
              {title} {isCustomEntry && '[Custom entry]'}
            </CardHeading>
            {!isPublicMode && (
              <MoreActionsMenu>
                <MoreActionsMenuButton />
                <MoreActionsMenuList>
                  <MoreActionsMenuItem
                    icon={<Edit3 size={16} />}
                    onClick={onEdit}
                  >
                    Edit
                  </MoreActionsMenuItem>
                  {isShareable && (
                    <MoreActionsMenuItem
                      icon={<EyeOff size={16} />}
                      onClick={() => {
                        hideModal.onOpen();
                        // trackEventInFlow(AnalyticsFlow.ConditionManaged, AnalyticsEventName.ConditionManaged, {
                        //   [EventPropsNames.Action]: 'Share',
                        //   [EventPropsNames.EntryPoint]: 'Menu edit button',
                        //   [EventPropsNames.ScreenName]: 'Conditions screen',
                        // });
                      }}
                    >
                      Hide from sharing
                    </MoreActionsMenuItem>
                  )}
                  <MoreActionsMenuItem
                    icon={<TrashIcon size={16} />}
                    onClick={() => deleteModal.onOpen()}
                  >
                    Delete
                  </MoreActionsMenuItem>
                </MoreActionsMenuList>
              </MoreActionsMenu>
            )}
          </Flex>
          {diagnosedDate && <CardPerformedLabel mt="-14px">{diagnosedDate}</CardPerformedLabel>}
          {notes?.length > 0 && <CardDescription>{notes}</CardDescription>}
          {children}
          {inheritedFrom && <InheritedFromLabel>{inheritedFrom}</InheritedFromLabel>}
        </Flex>
      </Card>
    </>
  );
}

export default function ProfileSidebarConditions({ onClose, name, action }: ISidebarProps) {
  const toast = useToast();
  const navigate = useNavigate();
  const { isPublicMode } = usePublicSettings();
  const { PROFILE, EHR, CONDITIONS } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const [selectedCondition, setSelectedCondition] = useState<any>(null);
  const [chronicityValue, setChronicityValue] = useState<any>();
  const [conditionOptions, setConditionOptions]: any = useState<any[]>([]);
  const conditionModal = useModal();
  const { authenticatedUser } = useAuthService();
  const sharePatientId = localStorage.getItem('sharePatientId');

  const { answerList, deleteConditions, updateConditions } = useMasterConditionResponseList(
    !isPublicMode ? authenticatedUser?.id : sharePatientId
  );

  useEffect(() => {
    Promise.all([
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.CONDITIONS),
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.CONDITIONS_COURSE),
    ]).then(([conditions, chronicity]) => {
      setConditionOptions(
        conditions.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code }))
      );
      setChronicityValue(chronicity);
    });
  }, []);

  const { trackEventInFlow } = useAnalyticsService();
  const chronicConditionList =
    answerList?.filter(
      (answer: any) =>
        answer.extension?.length > 0 &&
        answer.extension.some((ext: any) =>
          ext.valueCodeableConcept?.coding?.some((coding: any) => coding.display === ConditionType.Chronic)
        )
    ) || [];

  const acuteConditionList =
    answerList?.filter(
      (answer: any) =>
        answer.extension?.length > 0 &&
        answer.extension.some((ext: any) =>
          ext.valueCodeableConcept?.coding?.some((coding: any) => coding.display === ConditionType.Acute)
        )
    ) || [];

  const onAddHandler = () => {
    setSelectedCondition(null);
    recordConditionEvents(trackEventInFlow, {
      co_entry_point: 'my health profile',
      EventName: 'ConditionAddStarted',
    });
    navigate(`/${PROFILE}/${EHR}/${CONDITIONS}/${ADD}`);
  };

  useEffect(() => {
    if (action === ADD) {
      conditionModal.modalDisclosure.onOpen();
    }
  }, [action]);

  const onEditHandler = (condition: any) => {
    setSelectedCondition(condition);
    conditionModal.modalDisclosure.onOpen();
    recordConditionEvents(trackEventInFlow, {
      co_entry_point: 'my health profile',
      EventName: 'ConditionInteracted',
    });
  };

  const onRemoveHandler = async (questionnaire: any) => {
    const deleteTask = 'Condition';
    const identifier = `${deleteIdentifier}:condition`;
    const payload: any = {
      questionnaireId: questionnaire.id,
      deleteTask,
      identifier,
    };
    try {
      setSelectedCondition(questionnaire);
      await deleteConditions(payload?.questionnaireId);
      recordConditionEvents(trackEventInFlow, {
        co_entry_point: 'my health profile',
        EventName: 'ConditionRemoved',
      });
      toast({
        title: 'Condition removed',
        description: 'Condition has been removed from the list',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (e) {
      toast({
        title: 'Error',
        description: 'An error occurred while removing the condition',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };
  const closeFn = () => {
    conditionModal.modalDisclosure.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'conditions'));
  };
  const removeNullKeys = (obj: any): any =>
    Array.isArray(obj)
      ? obj.map(removeNullKeys)
      : obj && typeof obj === 'object'
      ? Object.fromEntries(
          Object.entries(obj)
            .filter(([, v]) => v != null)
            .map(([k, v]) => [k, removeNullKeys(v)])
        )
      : obj;
  const extractEvidence = (evidence: any[]): any[] =>
    (evidence || []).map((e) => ({
      detail: (e.detail || []).map(({ reference }: any) => ({ reference })),
    }));

  const disableSharing = async (condition: any) => {
    let payloadCondition = await createCondition(authenticatedUser?.id, condition, true);
    payloadCondition.code = condition.code;
    payloadCondition.onsetPeriod = condition.onsetPeriod;
    payloadCondition.note = condition.note;
    payloadCondition.evidence = extractEvidence(condition.evidence);
    payloadCondition.extension[0].valueCodeableConcept = condition.extension[0].valueCodeableConcept;
    payloadCondition = removeNullKeys(payloadCondition);
    await updateConditions({ conditionId: condition?.id, payloadCondition });
    toast({
      title: `Successfully hidden from sharing.`,
      status: 'success',
      duration: 4000,
      isClosable: true,
    });
  };
  const formatDiagnosedDate = (c: any) => {
    const start = c?.onsetPeriod?.start;
    const end = c?.onsetPeriod?.end;
    if (!start) return '';
    const inactive = c?.clinicalStatus?.coding?.[0]?.code === 'inactive';
    return `${dayjs(start).format('DD-MM-YYYY')} - ${
      inactive ? (end ? dayjs(end).format('DD-MM-YYYY') : 'Inactive') : 'Present'
    }`;
  };
  const renderConditionList = (conditionList: any[], title: string) => {
    if (conditionList?.length === 0) {
      return <EmptyStateTabsCard title={`Update ${title}`} />;
    }
    return (
      <>
        {conditionList?.map((condition: any) => {
          const isShareable = condition?.meta?.tag?.[0]?.code === ConditionShareProfile.shareProfileTrue;

          return (
            <ConditionCard
              key={condition.id}
              title={condition?.code?.coding?.[0]?.display}
              notes={condition?.note?.[0]?.text}
              diagnosedDate={formatDiagnosedDate(condition)}
              isCustomEntry={condition.code.coding[0].code === `cn:${PATIENT_DEFINED}`}
              onEdit={() => onEditHandler(condition)}
              onRemove={() => onRemoveHandler(condition)}
              isShareable={isShareable}
              isPublicMode={isPublicMode}
              onHideFromSharing={() => {
                disableSharing(condition);
              }}
            >
              {condition?.evidence?.length > 0 && (
                <Flex
                  direction="column"
                  gap="2px"
                >
                  <LinkedDocumentsLabel />
                  <LinkedDocumentsCard records={useExtractDocumentResource(condition?.evidence)} />
                </Flex>
              )}
              {!isPublicMode && !isShareable && <HideFromSharingCard />}
            </ConditionCard>
          );
        })}
      </>
    );
  };
  return (
    <>
      <ModalProvider {...conditionModal}>
        <Modal
          variant={MODAL_VARIANTS.PERIWINKLE}
          title="Conditions"
          primaryButtonLabel={selectedCondition ? 'Save' : 'Add'}
          showSecondaryButton={false}
          isCentered
          {...conditionModal.modalProps}
          {...conditionModal.modalDisclosure}
          onClose={closeFn}
        >
          <Suspense fallback={<FormSkeleton />}>
            <ConditionForm
              condition={selectedCondition}
              conditionOptions={conditionOptions}
              conditionTypeOptions={chronicityValue?.compose?.include?.[0]?.concept}
              name={name ?? ''}
            />
          </Suspense>
        </Modal>
      </ModalProvider>
      <Container
        position="relative"
        height="full"
        overflowY="scroll"
        overflowX="hidden"
        className="hide-scrollbar"
      >
        <Stack
          py="4"
          height="full"
        >
          <Flex justifyContent="space-between">
            <SidebarHeading>Conditions</SidebarHeading>
            <SidebarCloseButton onClick={onClose} />
          </Flex>
          {chronicConditionList?.length === 0 && acuteConditionList?.length === 0 ? (
            <SidebarEmptyState
              actionButtonText="Add"
              title="Update conditions"
              imageSrc="/empty-card-condition.png"
              completeInfoText={isPublicMode ? undefined : '+10% to complete your profile'}
              {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
              isPublicMode={isPublicMode}
            />
          ) : (
            <>
              {!isPublicMode && <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>}
              <RoundedTabs>
                <RoundedTabList borderColor="fluentHealthSecondary.400">
                  <RoundedTab>Chronic</RoundedTab>
                  <RoundedTab>Acute</RoundedTab>
                </RoundedTabList>
                <RoundedTabPanels>
                  <RoundedTabPanel>{renderConditionList(chronicConditionList, 'chronic conditions')}</RoundedTabPanel>
                  <RoundedTabPanel>{renderConditionList(acuteConditionList, 'acute conditions')}</RoundedTabPanel>
                </RoundedTabPanels>
              </RoundedTabs>
              <Spacer />
            </>
          )}
          {!isPublicMode && (
            <SidebarHelperTooltip
              text="What is a condition?"
              tooltipText={`Often synonymous with 'health condition', condition is a broad term that refers to a person's state of health that generally requires care and/or treatment. It may refer to a normal state with regard to one's health, such as pregnancy, or to an abnormal state due to disease, disorder, illness, or injury. It presents with symptoms, and is not a passing illness or injury. Listing medical conditions that you or your family members may be dealing with—including parents, siblings, grandparents, aunts/uncles—can help doctors provide you with better care. Knowing conditions that are common in your family can also help you take proactive steps toward your health by allowing you to recognise potential risks early on. \n Source: Ministry of Health and Family Welfare`}
            />
          )}
        </Stack>
      </Container>
    </>
  );
}
