import { Edit3, Trash as TrashIcon } from 'react-feather';
import { Flex, Spacer, Stack, Text, useDisclosure, useToast } from '@chakra-ui/react';
import { PropsWithChildren, Suspense, useEffect, useState } from 'react';
import { recordSupplementsEvents } from '@user/lib/events-analytics-manager';
import { useNavigate } from 'react-router-dom';

import { Medication } from '@lib/models/medication';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { MODAL_VARIANTS, Modal } from '../../../../../components/Modal';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from '../../../../../components/ui/Menu';
import SupplementForm from './SupplementForm';
import { Card, CardHeading, SidebarAddButton, SidebarHelperTooltip } from '../SidebarComponents';
import { FormSkeleton } from '../../../../../components/ui/Form';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { useMasterQuestionnaireList, useMedication } from 'src/app/user/lib/medplum-state';
import {
  MEDICATION_SUPPLEMENT,
  NavigationHelper,
  PATIENT_DEFINED,
  ROUTE_ACTIONS,
  ROUTE_VARIABLES,
} from 'src/app/user/lib/constants';
import { getValueSetByMasterList } from '@lib/utils/utils';
import { LinkedDocumentsCard, LinkedDocumentsLabel } from '../LinkedDocumentsCard';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../ConsentModal';
import { MEDPLUM_QUESTIONNAIRE, deleteIdentifier } from '@lib/constants';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';

function formatDosage(dosage: any[]): string {
  const timingOrder = ['MORN', 'AFT', 'EVE', 'NIGHT'];
  return timingOrder
    .map((time) => {
      const dose = dosage?.find((d) => d.timing?.repeat?.when?.includes(time));
      return dose?.doseAndRate?.[0]?.doseQuantity?.value || 0;
    })
    .join('-');
}

function SupplementCard({
  title,
  children,
  frequency,
  dosage,
  onRemove,
  onEdit,
  isPublicMode = false,
  isLoading,
  isCustomEntry,
}: PropsWithChildren<{
  title: string;
  children: any;
  dosage: string;
  frequency: string | undefined;
  onRemove: () => void;
  onEdit: () => void;
  isPublicMode: boolean;
  isLoading?: boolean;
  isCustomEntry?: boolean;
}>) {
  const deleteModal = useDisclosure();
  return (
    <>
      <ConsentModal {...deleteModal}>
        <ConsentModalHeading>
          Are you sure you want
          <br />
          to remove this entry?
        </ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            onClick={onRemove}
            isLoading={isLoading}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={deleteModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Card
        mt="4"
        pb="12px"
      >
        <Flex
          gap="8px"
          direction="column"
        >
          <Flex
            justifyContent="space-between"
            color="fluentHealthText.100"
          >
            <CardHeading maxWidth="90%">
              {title} {isCustomEntry && '[Custom entry]'}
            </CardHeading>
            {!isPublicMode && (
              <MoreActionsMenu>
                <MoreActionsMenuButton />
                <MoreActionsMenuList>
                  <MoreActionsMenuItem
                    icon={<Edit3 size={16} />}
                    onClick={onEdit}
                  >
                    Edit
                  </MoreActionsMenuItem>
                  <MoreActionsMenuItem
                    icon={<TrashIcon size={16} />}
                    onClick={() => deleteModal.onOpen()}
                  >
                    Delete
                  </MoreActionsMenuItem>
                </MoreActionsMenuList>
              </MoreActionsMenu>
            )}
          </Flex>
          {dosage !== '0-0-0-0' && (
            <Text
              bgColor="iris.500"
              color="white"
              rounded="sm"
              width="fit-content"
              borderRadius="4px"
              px="2"
              display="inline-flex"
              alignItems="center"
              maxWidth="50%"
              title={`${dosage}`}
            >
              {dosage}
            </Text>
          )}
          {frequency && (
            <Text
              bgColor="iris.500"
              color="white"
              rounded="sm"
              width="fit-content"
              borderRadius="4px"
              px="2"
              mb="4px"
              display="inline-flex"
              alignItems="center"
              maxWidth="50%"
              title={`${frequency}`}
            >
              {frequency}
            </Text>
          )}

          {children}
        </Flex>
      </Card>
    </>
  );
}

export function ProfileSidebarSupplements({ action }: { action: string | undefined }) {
  const toast = useToast();
  const navigate = useNavigate();
  const supplementModal = useDisclosure();
  const { trackEventInFlow } = useAnalyticsService();
  const { isPublicMode } = usePublicSettings();
  const { PROFILE, EHR, MEDICATIONS_SUPPLEMENTS, SUPPLEMENT } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const [submitting, setSubmitting]: any = useState<boolean>();

  const { authenticatedUser } = useAuthService();
  const { masterList } = useMasterQuestionnaireList(`${MEDPLUM_QUESTIONNAIRE}/${MEDICATION_SUPPLEMENT.SUPPLEMENT}`);
  const { medicationList, deleteMedication } = useMedication(authenticatedUser?.id);
  const updatedSupplementList = medicationList.filter((med: any) =>
    med.identifier?.some((id: any) => id.value === 'supplements')
  );
  const { masterList: masterList2 } = useMasterQuestionnaireList(
    `${MEDPLUM_QUESTIONNAIRE}/${MEDICATION_SUPPLEMENT.MEDICATION}`
  );

  const [selectedMedication, setSelectedMedication] = useState<Medication | null>(null);
  const [, setError] = useState<any>(null);
  const filteredMasterList = (id: string) => {
    const foundItem = masterList?.[0]?.item?.find((i: any) => i.linkId === id);
    return foundItem || null;
  };
  const supOptions: any = getValueSetByMasterList(
    masterList,
    filteredMasterList('name-of-supplement')?.answerValueSet ?? ''
  );
  const medicationAdministeredOptions: any = getValueSetByMasterList(
    masterList2,
    filteredMasterList('how-medication-administered')?.answerValueSet ?? '',
    'https://fluentinhealth.com/FHIR/ValueSet/MedicationAdministered'
  );
  const onAddHandler = () => {
    setSelectedMedication(null);
    recordSupplementsEvents(trackEventInFlow, {
      EventName: 'SupplementsAddStarted',
      su_entry_point: 'my_health_profile',
    });
    navigate(`/${PROFILE}/${EHR}/${MEDICATIONS_SUPPLEMENTS}/${SUPPLEMENT}/${ADD}`);
  };

  const onEditHandler = (medication: Medication) => {
    setSelectedMedication(medication);
    recordSupplementsEvents(trackEventInFlow, {
      EventName: 'SupplementsInteracted',
    });
    supplementModal.onOpen();
  };

  const onRemoveHandler = async (medication: Medication) => {
    try {
      const identifier = `${deleteIdentifier}:supplement`;
      await deleteMedication({ medicationId: medication.id, identifier });
      recordSupplementsEvents(trackEventInFlow, {
        EventName: 'SupplementsRemoved',
        su_name: medication.drug_name,
      });
      toast({
        title: 'Medication removed',
        description: 'Your medication has been removed.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      setError(error);
    }
  };

  useEffect(() => {
    if (action === ADD) {
      supplementModal.onOpen();
    }
  }, [action]);

  const closeFn = () => {
    supplementModal.onClose();
    if (action === ADD) navigate(NavigationHelper.getEhrView(false, 'medications-supplements', 'supplement'));
  };
  const formSubmiting = (incoming: boolean) => {
    setSubmitting(incoming);
  };
  return (
    <>
      <Modal
        variant={MODAL_VARIANTS.PERIWINKLE}
        title="Supplements"
        showModalFooter={false}
        isCentered
        {...supplementModal}
        onClose={closeFn}
      >
        <Suspense fallback={<FormSkeleton />}>
          {submitting ? (
            <FormSkeleton />
          ) : (
            <SupplementForm
              supplementOptions={supOptions}
              closeDialog={closeFn}
              supplementAdministeredOptions={medicationAdministeredOptions}
              supplement={selectedMedication}
              formSubmiting={formSubmiting}
            />
          )}
        </Suspense>
        <Suspense fallback={<FormSkeleton />} />
      </Modal>
      <Stack
        py="4"
        height="full"
      >
        {updatedSupplementList?.length === 0 && (
          <SidebarEmptyState
            actionButtonText="Add"
            title="Update supplement details"
            imageSrc="/empty-card-condition.png"
            completeInfoText={isPublicMode ? undefined : '+6% to complete your profile'}
            {...(isPublicMode ? { hideActionButton: true } : { onClick: onAddHandler })}
            isPublicMode={isPublicMode}
          />
        )}
        {!isPublicMode && updatedSupplementList?.length !== 0 && (
          <SidebarAddButton onClick={onAddHandler}>Add</SidebarAddButton>
        )}

        {updatedSupplementList?.map((answer: any) => {
          const title = answer?.medicationCodeableConcept?.coding?.[0]?.display;
          const dosage = formatDosage(answer?.dosage);
          const frequency =
            answer?.dosage?.[0]?.timing?.repeat?.boundsDuration?.value &&
            answer?.dosage?.[0]?.timing?.repeat?.boundsDuration?.value !== 0
              ? `${answer.dosage[0].timing.repeat.boundsDuration.value} ${answer.dosage[0].timing.repeat.boundsDuration.unit}`
              : undefined;

          return (
            <SupplementCard
              key={answer.id}
              title={title}
              dosage={dosage}
              frequency={frequency}
              onEdit={() => onEditHandler(answer)}
              onRemove={() => onRemoveHandler(answer)}
              isPublicMode={isPublicMode}
              isCustomEntry={answer.medicationCodeableConcept?.coding?.[0]?.code === `su:${PATIENT_DEFINED}`}
            >
              {answer.derivedFrom?.length > 0 && (
                <Flex
                  direction="column"
                  gap="2px"
                >
                  <LinkedDocumentsLabel />
                  <LinkedDocumentsCard records={useExtractDocumentResource(answer.derivedFrom)} />
                </Flex>
              )}
            </SupplementCard>
          );
        })}

        <Spacer />
        {!isPublicMode && (
          <SidebarHelperTooltip
            text="What is a supplement?"
            tooltipText="A supplement is a product that complements a person's regular diet that may be lacking in their regular food intake above the age of five. Food and Safety Standards explain health supplements must contain a concentrated source of one or more nutrients, such as amino acids, enzymes, minerals, proteins, vitamins, other dietary substances, plants or botanicals, prebiotics, probiotics, and animal-derived substances, as well as other similar substances with known and established nutritional or physiological effects, which are presented as such and offered alone or in combination. Supplements should ideally complement a balanced diet, not replace it."
          />
        )}
      </Stack>
    </>
  );
}
