// Package modules
import React, { useCallback, useEffect, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod/dist/zod';
import { z } from 'zod';
import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Grid,
  Input,
  InputGroup,
  InputLeftElement,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { phoneNumberRegex } from '@utils/regex';
import { HealthcareProxyFormValues } from '@src/types/forms';
import { HealthcareProxyContactPayLoad } from '@user/lib/models/healthcare-proxy';
import { useHealthcareProxyList } from '@user/lib/medplum-state';
import { medplumApi } from '@user/lib/medplum-api';
import { recordAdditionalMedicalDecisionMakerEvents } from '@user/lib/events-analytics-manager';
import { DOCUMENT_REF, NavigationHelper } from '@user/lib/constants';
import { useNavigate } from 'react-router-dom';

import { FHIR_HL7_CODE_SYSTEM_ROLE_CODE } from 'src/constants/medplumConstants';
import { useModalContext } from '../../../../../components/Modal';
import {
  DRAG_AND_DROP_AREA_VARIANT,
  DragAndDropAlertPrompt,
  DragAndDropArea,
  DragAndDropFilePreview,
  DragPrompt,
  useDragAndDropArea,
} from '../../../../../components/ui/DragAndDrop';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { HealthcareProxy } from '../../../lib/models/patient';
import { handleKeyPressNumbers, parsePatientName, parsePhoneNo } from '@lib/utils/utils';
import { Select, SelectOptionProps } from '../../../../../components/ui/Select';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../../components/ConsentModal';
import { FACT_CODE_SYSTEM } from '@lib/constants';

const URN_ADD_HEALTHCARE_PROXY_BUNDLE = 'urn:uuid:d005f249-acf0-472a-b6ea-8a6a8166c3c8';

const ACCEPTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
};

const MAX_FILES = 1;

// Helpers
const getInitialData: any = (contact?: HealthcareProxy | null) => {
  const name = parsePatientName(contact?.name, '');
  const [firstName = '', lastName = ''] = name.split(' ');
  const relation = contact?.relationship?.find((item: any) => {
    return item.coding?.some((coding: any) => coding.code !== 'HPOWATT');
  });
  const coding = relation?.coding?.[0];
  return {
    firstName,
    lastName,
    relation: {
      value: coding?.code || '',
      label: coding?.display || '',
    },
    phoneNumber: parsePhoneNo(contact?.telecom) ?? '',
    isMedicalDecisionMaker: true,
    file: contact?.file ?? [],
  };
};

export default function HealthcareProxyForm({ contact }: { contact: HealthcareProxy | null }) {
  const [, setError] = useState<any>(null);
  const { authenticatedUser: patient } = useAuthService();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const { addContact, deleteContact, updateContact, deleteFile } = useHealthcareProxyList(patient.id);
  const { isPublicMode } = usePublicSettings();

  const [reationShipGrp, setReationShipGrp] = useState<SelectOptionProps[]>([]);
  useEffect(() => {
    Promise.all([medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.RELATIONSHIP)]).then(
      ([Relationship]) => {
        setReationShipGrp(
          Relationship.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code }))
        );
      }
    );
  }, []);
  // Add/edit form type
  const isEditForm = contact !== null;

  const { files, uploadFilesHandler, removeFileHandler } = useDragAndDropArea({
    maxFiles: MAX_FILES,
  });

  const toast = useToast();
  const { modalDisclosure, setModalProps } = useModalContext();

  const form: any = useForm({
    mode: 'onChange',
    resolver: zodResolver(
      z.object({
        firstName: z.string().min(1).max(50),
        lastName: z.string().min(1).max(50),
        relation: z.object({
          value: z.string().optional(),
          label: z.string().optional(),
        }),
        phoneNumber: z.string().regex(phoneNumberRegex, 'Phone number is invalid'),
      })
    ),
    defaultValues: getInitialData(contact),
  });
  const {
    handleSubmit,
    control,
    formState: { isSubmitting, isValid },
    reset,
    register,
    setError: setFormError,
  } = form;

  const removeHealthcareProxyModal = useDisclosure();
  const relationField = form.watch('relation');
  const uploadedFile = files.length > 0 ? files[0] : null;
  const { trackEventInFlow } = useAnalyticsService();

  const handleKeyPressLetters = (e: React.KeyboardEvent) => {
    if (!/[A-Za-z]/.test(e.key)) {
      e.preventDefault();
    }
  };

  const customRelationSelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('relation', { value: value?.value, label: value?.label });
    form.trigger('relation');
    recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, {
      EventName: 'AMDMInProgRelationship',
      amdm_relationship: value?.value,
    });
  }, []);

  async function removeFile(contactData: any, fileValues: any) {
    const contactFileId = contactData?.file[0]?.id;
    const deleteFilePayload: any = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          request: {
            method: 'DELETE',
            url: `${DOCUMENT_REF}/${contactFileId}`,
          },
        },
      ],
    };
    await deleteFile(deleteFilePayload);
    removeFileHandler(fileValues);
  }

  async function createFile(fileValues: any) {
    const { content } = fileValues[0] || {};
    const { attachment } = content[0] || {};

    const { url, title } = attachment || {};
    const response = await fetch(url, { mode: 'no-cors' });
    const data = await response.blob();
    const metaData = {
      type: 'application/pdf',
    };
    const file = new File([data], title, metaData);
    return file;
  }
  const { file: fileValues } = form.formState.defaultValues || {};

  async function onSubmit(data: HealthcareProxyFormValues) {
    try {
      const fileRes = uploadedFile ? await medplumApi.uploadFileHandler(uploadedFile) : {};
      const { data: fileData, config } = fileRes || {};
      const { contentType, url, id: fileId } = fileData || {};
      const { name } = uploadedFile || {};
      const { data: configData } = config || {};
      const { size } = configData || {};
      const payload: any = {
        resourceType: 'Bundle',
        type: 'transaction',
        entry: [
          {
            fullUrl: URN_ADD_HEALTHCARE_PROXY_BUNDLE,
            resource: {
              resourceType: 'RelatedPerson',
              patient: {
                reference: `Patient/${patient.id}`,
              },
              relationship: [
                {
                  coding: [
                    {
                      system: FHIR_HL7_CODE_SYSTEM_ROLE_CODE,
                      code: 'HPOWATT',
                      display: 'healthcare power of attorney',
                    },
                  ],
                },
              ],
              name: [
                {
                  family: data?.lastName ?? '',
                  given: [data?.firstName ?? ''],
                },
              ],
              telecom: data?.phoneNumber ? [{ system: 'phone', value: data?.phoneNumber, use: 'home' }] : [],
              active: true,
            },
            request: {
              method: 'POST',
              url: 'RelatedPerson',
            },
          },
          {
            resource: {
              status: 'active',
              subject: {
                reference: `Patient/${patient.id}`,
              },
              content: uploadedFile
                ? [
                    {
                      attachment: {
                        contentType,
                        url: `Binary/${fileId}`,
                        title: name,
                        size,
                        id: fileId,
                      },
                    },
                  ]
                : [],
              context: {
                related: [
                  {
                    reference: URN_ADD_HEALTHCARE_PROXY_BUNDLE,
                  },
                ],
              },
              resourceType: DOCUMENT_REF,
            },
            request: {
              method: 'POST',
              url: DOCUMENT_REF,
            },
          },
        ],
      };

      if (data?.relation && payload.entry[0].resource.relationship?.length) {
        payload.entry[0].resource.relationship.push({
          coding: [
            {
              system: FHIR_HL7_CODE_SYSTEM_ROLE_CODE,
              code: data?.relation.value,
              display: data?.relation?.label,
            },
          ],
        });
      }

      try {
        if (isEditForm) {
          const updatePayload: HealthcareProxyContactPayLoad = {
            firstName: data.firstName,
            lastName: data.lastName,
            phoneNumber: data.phoneNumber,
            file: url
              ? [
                  {
                    attachment: {
                      contentType,
                      url: `Binary/${fileId}`,
                      title: String(name),
                      size,
                      id: fileId,
                    },
                  },
                ]
              : null,
          };
          if (uploadedFile) {
            updatePayload.file = {
              resourceType: 'Bundle',
              type: 'transaction',
              entry: [
                {
                  resource: {
                    status: 'active',
                    subject: {
                      reference: `Patient/${patient.id}`,
                    },
                    content: uploadedFile ? [{ attachment: { contentType, url, title: name, size, id: fileId } }] : [],
                    context: {
                      related: [
                        {
                          reference: `RelatedPerson/${contact?.id}`,
                        },
                      ],
                    },
                    resourceType: DOCUMENT_REF,
                  },
                  request: {
                    method: 'POST',
                    url: DOCUMENT_REF,
                  },
                },
              ],
            };
          }
          if (data?.relation) {
            const options = reationShipGrp.find((item: any) => item.value === data?.relation?.value);
            if (options) {
              const { value, label } = options;
              updatePayload.relationLabel = typeof label === 'string' ? label : String(label ?? '');
              updatePayload.relationCode = String(value);
            }
          }
          await updateContact({ contactId: contact!.id, updatePayload });
          toast({
            title: 'Successfully edited the Alternative Medical Decision-Maker',
            status: 'success',
            duration: 4000,
            isClosable: true,
          });
        } else {
          await addContact(payload);
          if (uploadedFile) {
            try {
              toast({
                title: 'Successfully added the Alternative Medical Decision-Maker',
                status: 'success',
                duration: 4000,
                isClosable: true,
              });
            } catch (error) {
              setError(error);
            }
          }
        }
      } catch (error) {
        toast({
          title: (error as any).message ?? 'Something went wrong!',
          status: 'error',
          duration: 4000,
          isClosable: true,
        });
      }

      // Clear form if "add" mode
      if (!isEditForm) reset();

      modalDisclosure.onClose();
    } catch (err) {
      setError(err);
    } finally {
      recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, {
        EventName: isEditForm ? 'AMDMEditCompleted' : 'AMDMAddCompleted',
        amdm_first_name: data.firstName,
        amdm_last_name: data.lastName,
        amdm_mobile_number: data.phoneNumber,
        amdm_relationship: data.relation?.value,
        amdm_record_added: !!uploadedFile,
      });
      navigate(NavigationHelper.getBasicsView(true, { absolutePath: true }));
    }
  }

  // Used in formatting error messages
  function onFormError(data: any) {
    if (data.firstName) {
      setFormError('firstName', { message: 'First name cannot be empty.' });
    }
    if (data.lastName) {
      setFormError('lastName', { message: 'Last name cannot be empty.' });
    }
    if (data.phoneNumber) {
      setFormError('phoneNumber', { message: 'The phone number is invalid.' });
    }
  }
  async function onRemoveHealthcareProxy() {
    try {
      setIsLoading(true);
      if (!contact?.id) return;
      await deleteContact(contact!.id);
      toast({
        title: 'Successfully removed the Alternative Medical Decision-Maker',
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
      recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, {
        EventName: 'AMDMRemoved',
      });
      removeHealthcareProxyModal.onClose();
      modalDisclosure.onClose();
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }

  const [formChanged, setFormChanged] = useState(false);

  useEffect(() => {
    const subscription = form.watch(() => {
      setFormChanged(true);
    });

    return () => subscription.unsubscribe();
  }, [form]);

  useEffect(() => {
    setModalProps((prevState: any) => ({
      ...prevState,
      primaryButtonEnabled: isValid && (formChanged || !isEditForm),
      isPrimaryButtonLoading: isSubmitting || isLoading,
      onPrimaryButtonClick: handleSubmit(onSubmit, onFormError),
      onTertiaryButtonClick: removeHealthcareProxyModal.onOpen,
    }));
  }, [isEditForm, isValid, isSubmitting, isLoading, handleSubmit, files, formChanged]);

  useEffect(() => {
    async function fetchFiles() {
      if (fileValues?.length > 0) {
        const response = await createFile(fileValues);
        uploadFilesHandler([response]);
      }
    }
    fetchFiles();
  }, [fileValues]);
  const { content } = (fileValues && fileValues[0]) || {};
  const { attachment } = (content && content[0]) || {};
  const { size } = attachment || {};

  return (
    <FormProvider {...form}>
      <ConsentModal {...removeHealthcareProxyModal}>
        <ConsentModalHeading>Are you sure you want to remove this entry?</ConsentModalHeading>
        <ConsentModalContent>This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            isLoading={isLoading}
            onClick={onRemoveHealthcareProxy}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={removeHealthcareProxyModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Flex direction="column">
        <Box
          display="flex"
          flexDirection="column"
          gap="7"
          py="4"
        >
          <Flex gap="24px">
            <FormControl
              variant="floating"
              isInvalid={!!form.formState.errors.firstName}
            >
              <Input
                onKeyDown={handleKeyPressLetters}
                placeholder=" "
                onBlurCapture={(e) =>
                  recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, {
                    EventName: 'AMDMInProgFirstName',
                    amdm_first_name: e.target.value,
                  })
                }
                {...register('firstName')}
              />
              <FormLabel>First Name*</FormLabel>
              <FormErrorMessage>First name is required</FormErrorMessage>
            </FormControl>

            <FormControl
              variant="floating"
              isInvalid={!!form.formState.errors.lastName}
            >
              <Input
                onKeyDown={handleKeyPressLetters}
                placeholder=" "
                {...register('lastName')}
                onBlurCapture={(e) =>
                  recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, {
                    EventName: 'AMDMInProgLastName',
                    amdm_last_name: e.target.value,
                  })
                }
              />
              <FormLabel>Last Name*</FormLabel>
              <FormErrorMessage>Last name is required</FormErrorMessage>
            </FormControl>
          </Flex>
          <Grid
            templateColumns="repeat(2, 1fr)"
            gap="24px"
          >
            <FormControl isInvalid={!!form.formState.errors.relation}>
              <Select
                labelText="Relationship"
                value={reationShipGrp.find((item: any) => item.label === relationField?.label)}
                onChange={customRelationSelect}
                options={reationShipGrp}
                isSearchable={false}
                isClearable
                menuPosition="fixed"
              />
              <FormErrorMessage>This field is required</FormErrorMessage>
            </FormControl>
            <FormControl
              variant="floatingTel"
              isInvalid={!!form.formState.errors.phoneNumber}
            >
              <Controller
                name="phoneNumber"
                control={control}
                render={({ field }) => (
                  <InputGroup className={field.value ? 'has__value' : ''}>
                    <InputLeftElement width="auto">+ 91</InputLeftElement>
                    <Input
                      placeholder=" "
                      onKeyDown={handleKeyPressNumbers}
                      {...register('phoneNumber')}
                      onBlurCapture={(e) =>
                        recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, {
                          EventName: 'AMDMInProgMobileNumber',
                          amdm_mobile_number: e.target.value,
                        })
                      }
                      maxLength={10}
                    />
                    <FormLabel>Mobile Number*</FormLabel>
                  </InputGroup>
                )}
              />
              <FormErrorMessage>{form.formState.errors.phoneNumber?.message}</FormErrorMessage>
            </FormControl>
          </Grid>
          {files && files.length > 0 ? (
            <Box>
              <Text
                size="sm"
                color="fluentHealthText.400"
                mb="4px"
              >
                File
              </Text>
              {files.map((file) => (
                <DragAndDropFilePreview
                  key={file.name}
                  file={file}
                  size={size}
                  onRemove={() => {
                    removeFile(contact, file);
                    recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, {
                      EventName: 'AMDMInProgRecordAdded',
                      amdm_record_added: false,
                    });
                    setFormChanged(true);
                  }}
                />
              ))}
            </Box>
          ) : (
            <Box>
              <Text
                color="gray.200"
                mb="4px"
              >
                Upload file (Optional)
              </Text>
              <DragAndDropArea
                acceptedFileTypes={ACCEPTED_FILE_TYPES}
                onFilesDrop={(e) => {
                  uploadFilesHandler(e);
                  recordAdditionalMedicalDecisionMakerEvents(trackEventInFlow, {
                    EventName: 'AMDMInProgRecordAdded',
                    amdm_record_added: true,
                  });
                  setFormChanged(true);
                }}
                disabled={files.length >= MAX_FILES}
                maxFiles={MAX_FILES}
              >
                {(props) => (
                  <DragPrompt
                    variant={DRAG_AND_DROP_AREA_VARIANT.VERTICAL}
                    height="200px"
                    alertPrompt={
                      <DragAndDropAlertPrompt
                        m="24px"
                        mb="-14px"
                      >
                        PDF file that do not exceed 25 MB.
                      </DragAndDropAlertPrompt>
                    }
                    {...props}
                  />
                )}
              </DragAndDropArea>
            </Box>
          )}
        </Box>
      </Flex>
    </FormProvider>
  );
}
