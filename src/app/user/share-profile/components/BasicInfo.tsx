import React from 'react';
import { Flex, Text } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';
import { useSharePatient } from '@user/lib/state';

import EmptyState from './EmptyState';
import { ProfileBioCard } from './ProfileBioCard';

function MobileBasicInfo() {
  const { patient } = useSharePatient();

  return (
    <Flex direction="column">
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
      >
        My personal details
      </Text>
      <ProfileBioCard patient={patient} />
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
      >
        ADDITIONAL DETAILS
      </Text>
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Emergency Contacts
      </Text>
      <EmptyState
        message="Nothing here yet"
        margin="16px 0px 0px 0px"
      />
      <Text
        fontSize="13"
        fontWeight="medium"
        color="iris.500"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Alternative Medical Decision-Maker
      </Text>
      <EmptyState
        message="Nothing here yet"
        margin="16px 0px 0px 0px"
      />
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Health insurance
      </Text>
      <EmptyState
        message="Nothing here yet"
        margin="16px 0px 0px 0px"
      />
    </Flex>
  );
}
export default function BasicInfo() {
  const isTablet = useIsTablet();

  if (isTablet) {
    return <MobileBasicInfo />;
  }
  return (
    <Flex direction="column">
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
      >
        ADDITIONAL DETAILS
      </Text>
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Emergency Contacts
      </Text>
      <EmptyState
        message="Nothing here yet"
        margin="16px 0px 0px 0px"
      />
      <Text
        fontSize="13"
        fontWeight="medium"
        color="iris.500"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Alternative Medical Decision-Maker
      </Text>
      <EmptyState
        message="Nothing here yet"
        margin="16px 0px 0px 0px"
      />
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Health insurance
      </Text>
      <EmptyState
        message="Nothing here yet"
        margin="16px 0px 0px 0px"
      />
    </Flex>
  );
}
